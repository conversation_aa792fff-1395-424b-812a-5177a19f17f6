#!/usr/bin/env python3
"""
API Endpoints Testing
Test all FastAPI endpoints with real requests and responses.
"""

import asyncio
import sys
import time
import json
import httpx
from pathlib import Path
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, str(Path('.').absolute()))

async def test_api_routes_directly():
    """Test API routes directly without starting server."""
    print('\n=== DIRECT API ROUTES TEST ===')
    
    try:
        from app.api.app import create_app
        from fastapi.testclient import TestClient
        
        # Create test client
        app = create_app()
        client = TestClient(app)
        
        results = {}
        
        # Test 1: Health endpoint
        print('1. Testing health endpoint...')
        response = client.get("/health")
        results['health'] = {
            'status_code': response.status_code,
            'response': response.json() if response.status_code == 200 else response.text
        }
        print(f'   ✓ Health endpoint: {response.status_code} - {results["health"]["response"]}')
        
        # Test 2: Market data endpoints
        print('2. Testing market data endpoints...')
        
        # Test market data list
        response = client.get("/api/v1/market-data?limit=5")
        results['market_data_list'] = {
            'status_code': response.status_code,
            'count': len(response.json()) if response.status_code == 200 else 0
        }
        print(f'   ✓ Market data list: {response.status_code} - {results["market_data_list"]["count"]} items')
        
        # Test trending coins
        response = client.get("/api/v1/market-data/trending")
        results['trending'] = {
            'status_code': response.status_code,
            'count': len(response.json()) if response.status_code == 200 else 0
        }
        print(f'   ✓ Trending coins: {response.status_code} - {results["trending"]["count"]} items')
        
        # Test global market data
        response = client.get("/api/v1/market-data/global")
        results['global_data'] = {
            'status_code': response.status_code,
            'has_data': bool(response.json()) if response.status_code == 200 else False
        }
        print(f'   ✓ Global market data: {response.status_code} - {"Data available" if results["global_data"]["has_data"] else "No data"}')
        
        # Test 3: Quantum endpoints
        print('3. Testing quantum endpoints...')
        
        # Test quantum score calculation
        test_data = {
            "tokens": [
                {"symbol": "BTC", "price_change_24h": 2.5, "volume_ratio": 0.05, "market_cap_rank": 1},
                {"symbol": "ETH", "price_change_24h": -1.2, "volume_ratio": 0.08, "market_cap_rank": 2}
            ]
        }
        
        response = client.post("/api/v1/quantum/calculate-scores", json=test_data)
        results['quantum_scores'] = {
            'status_code': response.status_code,
            'response': response.json() if response.status_code == 200 else response.text[:200]
        }
        print(f'   ✓ Quantum scores: {response.status_code} - {results["quantum_scores"]["response"]}')
        
        # Test quantum model info
        response = client.get("/api/v1/quantum/model-info")
        results['quantum_model_info'] = {
            'status_code': response.status_code,
            'response': response.json() if response.status_code == 200 else response.text[:200]
        }
        print(f'   ✓ Quantum model info: {response.status_code}')
        
        # Test 4: Scraper endpoints
        print('4. Testing scraper endpoints...')
        
        # Test scraper status
        response = client.get("/api/v1/scrapers/status")
        results['scraper_status'] = {
            'status_code': response.status_code,
            'scrapers_count': len(response.json()) if response.status_code == 200 else 0
        }
        print(f'   ✓ Scraper status: {response.status_code} - {results["scraper_status"]["scrapers_count"]} scrapers')
        
        # Test scraper metrics
        response = client.get("/api/v1/scrapers/metrics")
        results['scraper_metrics'] = {
            'status_code': response.status_code,
            'has_metrics': bool(response.json()) if response.status_code == 200 else False
        }
        print(f'   ✓ Scraper metrics: {response.status_code} - {"Metrics available" if results["scraper_metrics"]["has_metrics"] else "No metrics"}')
        
        return True, results
        
    except Exception as e:
        print(f'   ✗ Direct API routes test failed: {e}')
        import traceback
        traceback.print_exc()
        return False, {}

async def test_individual_components():
    """Test individual API components."""
    print('\n=== INDIVIDUAL COMPONENT TEST ===')
    
    try:
        # Test market data route functions
        from app.api.routes.market_data import get_market_data, get_trending_coins, get_global_data
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        results = {}
        
        # Test 1: Market data function
        print('1. Testing market data function...')
        scraper = CoinGeckoScraper()
        market_data = await get_market_data(limit=5, page=1, scraper=scraper)
        results['market_data_function'] = {
            'count': len(market_data),
            'sample': market_data[0] if market_data else None
        }
        print(f'   ✓ Market data function: {len(market_data)} items')
        
        # Test 2: Trending coins function
        print('2. Testing trending coins function...')
        trending = await get_trending_coins(scraper=scraper)
        results['trending_function'] = {
            'count': len(trending),
            'sample': trending[0] if trending else None
        }
        print(f'   ✓ Trending coins function: {len(trending)} items')
        
        # Test 3: Global data function
        print('3. Testing global data function...')
        global_data = await get_global_data(scraper=scraper)
        results['global_data_function'] = {
            'has_data': bool(global_data),
            'keys': list(global_data.keys()) if global_data else []
        }
        print(f'   ✓ Global data function: {"Data available" if results["global_data_function"]["has_data"] else "No data"}')
        
        await scraper.close()
        
        return True, results
        
    except Exception as e:
        print(f'   ✗ Individual component test failed: {e}')
        return False, {}

async def test_quantum_api_components():
    """Test quantum API components."""
    print('\n=== QUANTUM API COMPONENTS TEST ===')
    
    try:
        from app.api.routes.quantum import calculate_quantum_scores, get_model_info
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        
        results = {}
        
        # Test 1: Quantum score calculation
        print('1. Testing quantum score calculation...')
        calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        
        # Create test request data
        class MockRequest:
            def __init__(self, tokens):
                self.tokens = tokens
        
        test_tokens = [
            {"symbol": "BTC", "price_change_24h": 2.5, "volume_ratio": 0.05, "market_cap_rank": 1},
            {"symbol": "ETH", "price_change_24h": -1.2, "volume_ratio": 0.08, "market_cap_rank": 2},
            {"symbol": "SOL", "price_change_24h": 5.8, "volume_ratio": 0.12, "market_cap_rank": 5}
        ]
        
        mock_request = MockRequest(test_tokens)
        
        try:
            scores_response = await calculate_quantum_scores(mock_request, calculator)
            results['quantum_scores'] = {
                'success': True,
                'scores_count': len(scores_response.get('scores', [])),
                'response': scores_response
            }
            print(f'   ✓ Quantum scores: {results["quantum_scores"]["scores_count"]} scores calculated')
        except Exception as e:
            results['quantum_scores'] = {
                'success': False,
                'error': str(e)
            }
            print(f'   ✗ Quantum scores failed: {e}')
        
        # Test 2: Model info
        print('2. Testing model info...')
        try:
            model_info = await get_model_info(calculator)
            results['model_info'] = {
                'success': True,
                'info': model_info
            }
            print(f'   ✓ Model info: {model_info}')
        except Exception as e:
            results['model_info'] = {
                'success': False,
                'error': str(e)
            }
            print(f'   ✗ Model info failed: {e}')
        
        return True, results
        
    except Exception as e:
        print(f'   ✗ Quantum API components test failed: {e}')
        return False, {}

async def main():
    """Run comprehensive API endpoint validation."""
    print('=== API ENDPOINTS TESTING ===')
    print(f'Test started at: {datetime.now().isoformat()}')
    
    start_time = time.time()
    all_results = {}
    
    # Test 1: Direct API routes
    routes_success, routes_results = await test_api_routes_directly()
    all_results['api_routes'] = {'success': routes_success, 'data': routes_results}
    
    # Test 2: Individual components
    components_success, components_results = await test_individual_components()
    all_results['components'] = {'success': components_success, 'data': components_results}
    
    # Test 3: Quantum API components
    quantum_success, quantum_results = await test_quantum_api_components()
    all_results['quantum_api'] = {'success': quantum_success, 'data': quantum_results}
    
    # Final summary
    total_time = time.time() - start_time
    successful_tests = sum(1 for result in all_results.values() if result['success'])
    total_tests = len(all_results)
    
    print(f'\n=== API ENDPOINTS TESTING SUMMARY ===')
    print(f'Total test suites: {total_tests}')
    print(f'Successful: {successful_tests}')
    print(f'Failed: {total_tests - successful_tests}')
    print(f'Success rate: {successful_tests/total_tests*100:.1f}%')
    print(f'Total test time: {total_time:.2f} seconds')
    
    for test_name, result in all_results.items():
        status = "✓ PASS" if result['success'] else "✗ FAIL"
        print(f'  {test_name.replace("_", " ").title()}: {status}')
    
    # Save detailed results
    with open('api_validation_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f'\nDetailed results saved to: api_validation_results.json')

if __name__ == "__main__":
    asyncio.run(main())
