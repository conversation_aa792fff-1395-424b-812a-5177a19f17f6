#!/usr/bin/env python3
"""
Comprehensive End-to-End System Testing
Tests the entire Alpha Grid system with real data and production-like scenarios.
"""

import asyncio
import json
import time
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import traceback
import httpx
import numpy as np
from datetime import datetime, timedelta
import sqlite3

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

# Import all components
from app.core.config import settings, config_manager
from app.core.database import init_database, db_manager
from app.core.logging import setup_logging
from app.agents.scrapers.registry import scraper_registry
from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
from app.agents.scrapers.etherscan_scraper import EtherscanScraper
from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
from app.agents.quantum.quantum_feature_encoder import QuantumFeatureEncoder
from app.agents.quantum.quantum_circuit_optimizer import QuantumCircuitOptimizer
from app.models.market_data import MarketData
from app.models.scraper_data import ScraperData

# Setup logging
setup_logging()
import structlog
logger = structlog.get_logger(__name__)

class ComprehensiveSystemTest:
    """
    Comprehensive system testing with real data and production scenarios.
    Tests all components from scrapers to quantum scoring to API endpoints.
    """
    
    def __init__(self):
        self.results = {
            "test_start": datetime.now().isoformat(),
            "configuration": {},
            "scrapers": {},
            "quantum": {},
            "database": {},
            "api": {},
            "integration": {},
            "performance": {},
            "errors": []
        }
        self.start_time = time.time()
        
    async def run_all_tests(self):
        """Run all system tests in order."""
        logger.info("Starting comprehensive system testing")
        
        try:
            # Test 1: Configuration and Infrastructure
            await self.test_configuration()
            
            # Test 2: Database Operations
            await self.test_database_operations()
            
            # Test 3: Scraper Infrastructure with Real Data
            await self.test_scrapers_with_real_data()
            
            # Test 4: Quantum Computing System
            await self.test_quantum_system()
            
            # Test 5: API Endpoints
            await self.test_api_endpoints()
            
            # Test 6: Integration Tests
            await self.test_integration_flows()
            
            # Test 7: Performance Tests
            await self.test_performance()
            
            # Test 8: Error Handling
            await self.test_error_handling()
            
        except Exception as e:
            logger.error("Critical system test failure", error=str(e))
            self.results["errors"].append({
                "test": "system_wide",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
        
        finally:
            self.results["test_end"] = datetime.now().isoformat()
            self.results["total_duration"] = time.time() - self.start_time
            await self.generate_report()
    
    async def test_configuration(self):
        """Test configuration and environment setup."""
        logger.info("Testing configuration and environment")
        
        try:
            # Test config validation
            validation = config_manager.validate_configuration()
            self.results["configuration"]["validation"] = validation
            
            # Test API key access
            api_keys = {}
            for key in ["coingecko_api_key", "etherscan_api_key", "web3_provider_url"]:
                value = config_manager.get_decrypted_key(key)
                api_keys[key] = "present" if value else "missing"
            
            self.results["configuration"]["api_keys"] = api_keys
            
            # Test database path
            db_path = settings.database_url
            self.results["configuration"]["database_path"] = db_path
            
            # Test environment variables
            env_vars = {
                "debug": settings.debug,
                "log_level": settings.log_level,
                "max_workers": settings.max_workers
            }
            self.results["configuration"]["environment"] = env_vars
            
            logger.info("Configuration testing completed")
            
        except Exception as e:
            logger.error("Configuration test failed", error=str(e))
            self.results["errors"].append({
                "test": "configuration",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def test_database_operations(self):
        """Test database operations with real data."""
        logger.info("Testing database operations")
        
        try:
            # Initialize database
            await init_database()
            
            # Test connection
            async with db_manager.session() as session:
                # Test basic operations
                result = await session.execute("SELECT 1")
                assert result.scalar() == 1
                
                # Test table creation
                await session.execute("""
                    CREATE TABLE IF NOT EXISTS test_data (
                        id INTEGER PRIMARY KEY,
                        symbol TEXT,
                        price REAL,
                        volume REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Test data insertion
                test_data = [
                    ("BTC", 45000.0, 1000000.0),
                    ("ETH", 3000.0, 500000.0),
                    ("SOL", 100.0, 200000.0)
                ]
                
                for symbol, price, volume in test_data:
                    await session.execute(
                        "INSERT INTO test_data (symbol, price, volume) VALUES (?, ?, ?)",
                        (symbol, price, volume)
                    )
                
                # Test data retrieval
                result = await session.execute("SELECT COUNT(*) FROM test_data")
                count = result.scalar()
                
                # Test data accuracy
                result = await session.execute("SELECT * FROM test_data ORDER BY symbol")
                rows = result.fetchall()
                
                self.results["database"]["operations"] = {
                    "connection": "success",
                    "table_creation": "success",
                    "data_insertion": "success",
                    "data_retrieval": "success",
                    "record_count": count,
                    "sample_data": [dict(row) for row in rows[:3]]
                }
                
                # Clean up test data
                await session.execute("DROP TABLE test_data")
                await session.commit()
                
            logger.info("Database operations testing completed")
            
        except Exception as e:
            logger.error("Database test failed", error=str(e))
            self.results["errors"].append({
                "test": "database",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def test_scrapers_with_real_data(self):
        """Test scraper infrastructure with real API calls."""
        logger.info("Testing scrapers with real data")
        
        try:
            # Test CoinGecko scraper
            await self.test_coingecko_scraper()
            
            # Test Etherscan scraper
            await self.test_etherscan_scraper()
            
            # Test scraper registry
            await self.test_scraper_registry()
            
            logger.info("Scraper testing completed")
            
        except Exception as e:
            logger.error("Scraper test failed", error=str(e))
            self.results["errors"].append({
                "test": "scrapers",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def test_coingecko_scraper(self):
        """Test CoinGecko scraper with real API calls."""
        logger.info("Testing CoinGecko scraper")
        
        try:
            scraper = CoinGeckoScraper()
            
            # Test market data fetching
            market_data = await scraper.fetch_market_data(["bitcoin", "ethereum", "solana"])
            
            self.results["scrapers"]["coingecko"] = {
                "status": "success",
                "data_count": len(market_data),
                "sample_data": market_data[:2] if market_data else [],
                "timestamp": datetime.now().isoformat()
            }
            
            # Validate data structure
            if market_data:
                sample = market_data[0]
                required_fields = ["id", "symbol", "current_price", "market_cap", "total_volume"]
                missing_fields = [field for field in required_fields if field not in sample]
                
                if missing_fields:
                    self.results["scrapers"]["coingecko"]["validation_errors"] = missing_fields
                else:
                    self.results["scrapers"]["coingecko"]["validation"] = "passed"
            
            logger.info("CoinGecko scraper test completed", data_count=len(market_data))
            
        except Exception as e:
            logger.error("CoinGecko scraper test failed", error=str(e))
            self.results["scrapers"]["coingecko"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_etherscan_scraper(self):
        """Test Etherscan scraper with real API calls."""
        logger.info("Testing Etherscan scraper")
        
        try:
            scraper = EtherscanScraper()
            
            # Test gas price fetching
            gas_data = await scraper.fetch_gas_prices()
            
            # Test transaction count for a popular address
            eth_address = "******************************************"  # Vitalik's address
            tx_count = await scraper.fetch_transaction_count(eth_address)
            
            self.results["scrapers"]["etherscan"] = {
                "status": "success",
                "gas_data": gas_data,
                "transaction_count": tx_count,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Etherscan scraper test completed")
            
        except Exception as e:
            logger.error("Etherscan scraper test failed", error=str(e))
            self.results["scrapers"]["etherscan"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_scraper_registry(self):
        """Test scraper registry functionality."""
        logger.info("Testing scraper registry")
        
        try:
            # Test registry status
            status = await scraper_registry.get_health_status()
            
            # Test scraper management
            available_scrapers = scraper_registry.get_available_scrapers()
            
            self.results["scrapers"]["registry"] = {
                "status": "success",
                "health_status": status,
                "available_scrapers": available_scrapers,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Scraper registry test completed")
            
        except Exception as e:
            logger.error("Scraper registry test failed", error=str(e))
            self.results["scrapers"]["registry"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_quantum_system(self):
        """Test quantum computing system with real calculations."""
        logger.info("Testing quantum system")
        
        try:
            # Test quantum score calculator
            await self.test_quantum_score_calculator()
            
            # Test quantum feature encoder
            await self.test_quantum_feature_encoder()
            
            # Test quantum circuit optimizer
            await self.test_quantum_circuit_optimizer()
            
            logger.info("Quantum system testing completed")
            
        except Exception as e:
            logger.error("Quantum system test failed", error=str(e))
            self.results["errors"].append({
                "test": "quantum",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def test_quantum_score_calculator(self):
        """Test quantum score calculator with real market data."""
        logger.info("Testing quantum score calculator")
        
        try:
            calculator = QuantumScoreCalculator()
            
            # Test with realistic market features
            test_features = [
                {
                    "social_momentum": 0.75,
                    "onchain_activity": 0.85,
                    "technical_strength": 0.60,
                    "volume_momentum": 0.70,
                    "holder_growth": 0.65,
                    "symbol": "BTC"
                },
                {
                    "social_momentum": 0.60,
                    "onchain_activity": 0.70,
                    "technical_strength": 0.80,
                    "volume_momentum": 0.90,
                    "holder_growth": 0.55,
                    "symbol": "ETH"
                },
                {
                    "social_momentum": 0.90,
                    "onchain_activity": 0.45,
                    "technical_strength": 0.70,
                    "volume_momentum": 0.85,
                    "holder_growth": 0.80,
                    "symbol": "SOL"
                }
            ]
            
            scores = []
            for features in test_features:
                score = await calculator.calculate_score(features)
                scores.append({
                    "symbol": features["symbol"],
                    "score": score,
                    "features": features
                })
            
            self.results["quantum"]["score_calculator"] = {
                "status": "success",
                "test_cases": len(test_features),
                "scores": scores,
                "score_range": {
                    "min": min(s["score"] for s in scores),
                    "max": max(s["score"] for s in scores),
                    "mean": np.mean([s["score"] for s in scores])
                },
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Quantum score calculator test completed", test_cases=len(test_features))
            
        except Exception as e:
            logger.error("Quantum score calculator test failed", error=str(e))
            self.results["quantum"]["score_calculator"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_quantum_feature_encoder(self):
        """Test quantum feature encoder."""
        logger.info("Testing quantum feature encoder")
        
        try:
            encoder = QuantumFeatureEncoder()
            
            # Test feature encoding
            raw_features = {
                "price_change_24h": 0.05,
                "volume_change_24h": 0.15,
                "market_cap_rank": 1,
                "social_score": 0.8,
                "developer_activity": 0.7
            }
            
            encoded_features = encoder.encode_features(raw_features)
            
            self.results["quantum"]["feature_encoder"] = {
                "status": "success",
                "raw_features": raw_features,
                "encoded_features": encoded_features.tolist(),
                "encoding_range": {
                    "min": float(encoded_features.min()),
                    "max": float(encoded_features.max())
                },
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Quantum feature encoder test completed")
            
        except Exception as e:
            logger.error("Quantum feature encoder test failed", error=str(e))
            self.results["quantum"]["feature_encoder"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_quantum_circuit_optimizer(self):
        """Test quantum circuit optimizer."""
        logger.info("Testing quantum circuit optimizer")
        
        try:
            optimizer = QuantumCircuitOptimizer()
            
            # Test circuit optimization
            test_parameters = np.random.rand(5) * np.pi
            optimized_circuit = optimizer.optimize_circuit(test_parameters)
            
            self.results["quantum"]["circuit_optimizer"] = {
                "status": "success",
                "input_parameters": test_parameters.tolist(),
                "circuit_depth": optimized_circuit.depth(),
                "circuit_size": optimized_circuit.size(),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Quantum circuit optimizer test completed")
            
        except Exception as e:
            logger.error("Quantum circuit optimizer test failed", error=str(e))
            self.results["quantum"]["circuit_optimizer"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_api_endpoints(self):
        """Test FastAPI endpoints with real requests."""
        logger.info("Testing API endpoints")
        
        try:
            # Start the server in background for testing
            from app.api.app import create_app
            
            app = create_app()
            
            # Test endpoints using httpx
            async with httpx.AsyncClient(app=app, base_url="http://test") as client:
                # Test health endpoint
                response = await client.get("/api/v1/health/status")
                health_status = response.json()
                
                # Test market data endpoint
                response = await client.get("/api/v1/market/prices?symbols=bitcoin,ethereum")
                market_response = response.json()
                
                # Test quantum endpoint
                quantum_payload = {
                    "features": {
                        "social_momentum": 0.7,
                        "onchain_activity": 0.8,
                        "technical_strength": 0.6,
                        "volume_momentum": 0.75,
                        "holder_growth": 0.65
                    }
                }
                response = await client.post("/api/v1/quantum/score", json=quantum_payload)
                quantum_response = response.json()
                
                self.results["api"]["endpoints"] = {
                    "health": {
                        "status_code": response.status_code,
                        "response": health_status
                    },
                    "market_data": {
                        "status_code": response.status_code,
                        "response": market_response
                    },
                    "quantum": {
                        "status_code": response.status_code,
                        "response": quantum_response
                    }
                }
            
            logger.info("API endpoints testing completed")
            
        except Exception as e:
            logger.error("API endpoints test failed", error=str(e))
            self.results["api"]["endpoints"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_integration_flows(self):
        """Test end-to-end integration flows."""
        logger.info("Testing integration flows")
        
        try:
            # Test complete flow: scrape -> process -> score -> store
            # 1. Scrape real data
            scraper = CoinGeckoScraper()
            market_data = await scraper.fetch_market_data(["bitcoin"])
            
            if market_data:
                btc_data = market_data[0]
                
                # 2. Convert to features
                features = {
                    "social_momentum": 0.7,
                    "onchain_activity": 0.8,
                    "technical_strength": 0.6,
                    "volume_momentum": btc_data.get("total_volume", 0) / 1000000000,  # Normalize
                    "holder_growth": 0.65
                }
                
                # 3. Calculate quantum score
                calculator = QuantumScoreCalculator()
                score = await calculator.calculate_score(features)
                
                # 4. Store in database
                async with db_manager.session() as session:
                    await session.execute("""
                        CREATE TABLE IF NOT EXISTS integration_test (
                            id INTEGER PRIMARY KEY,
                            symbol TEXT,
                            price REAL,
                            quantum_score REAL,
                            features TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    
                    await session.execute(
                        "INSERT INTO integration_test (symbol, price, quantum_score, features) VALUES (?, ?, ?, ?)",
                        ("BTC", btc_data.get("current_price", 0), score, json.dumps(features))
                    )
                    
                    await session.commit()
                
                self.results["integration"]["complete_flow"] = {
                    "status": "success",
                    "steps": {
                        "scrape": "success",
                        "process": "success",
                        "score": score,
                        "store": "success"
                    },
                    "btc_data": btc_data,
                    "features": features,
                    "timestamp": datetime.now().isoformat()
                }
            
            logger.info("Integration flow testing completed")
            
        except Exception as e:
            logger.error("Integration flow test failed", error=str(e))
            self.results["integration"]["complete_flow"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_performance(self):
        """Test system performance with concurrent operations."""
        logger.info("Testing system performance")
        
        try:
            # Test concurrent scraping
            scraper = CoinGeckoScraper()
            symbols = ["bitcoin", "ethereum", "solana", "cardano", "polkadot"]
            
            start_time = time.time()
            tasks = [scraper.fetch_market_data([symbol]) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            concurrent_time = time.time() - start_time
            
            # Test quantum score calculation performance
            calculator = QuantumScoreCalculator()
            test_features = {
                "social_momentum": 0.7,
                "onchain_activity": 0.8,
                "technical_strength": 0.6,
                "volume_momentum": 0.75,
                "holder_growth": 0.65
            }
            
            start_time = time.time()
            scores = []
            for _ in range(10):
                score = await calculator.calculate_score(test_features)
                scores.append(score)
            quantum_time = time.time() - start_time
            
            self.results["performance"] = {
                "concurrent_scraping": {
                    "symbols": len(symbols),
                    "duration": concurrent_time,
                    "avg_per_symbol": concurrent_time / len(symbols),
                    "success_rate": len([r for r in results if not isinstance(r, Exception)]) / len(results)
                },
                "quantum_scoring": {
                    "calculations": len(scores),
                    "total_duration": quantum_time,
                    "avg_per_calculation": quantum_time / len(scores),
                    "scores_range": {
                        "min": min(scores),
                        "max": max(scores),
                        "std": np.std(scores)
                    }
                },
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Performance testing completed")
            
        except Exception as e:
            logger.error("Performance test failed", error=str(e))
            self.results["performance"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def test_error_handling(self):
        """Test error handling and recovery mechanisms."""
        logger.info("Testing error handling")
        
        try:
            error_tests = {}
            
            # Test invalid API key handling
            try:
                scraper = CoinGeckoScraper()
                scraper.api_key = "invalid_key"
                await scraper.fetch_market_data(["bitcoin"])
                error_tests["invalid_api_key"] = "unexpected_success"
            except Exception as e:
                error_tests["invalid_api_key"] = "handled_correctly"
            
            # Test invalid quantum features
            try:
                calculator = QuantumScoreCalculator()
                invalid_features = {
                    "social_momentum": "invalid",
                    "onchain_activity": None,
                    "technical_strength": -1,
                    "volume_momentum": 999,
                    "holder_growth": 0.5
                }
                await calculator.calculate_score(invalid_features)
                error_tests["invalid_quantum_features"] = "unexpected_success"
            except Exception as e:
                error_tests["invalid_quantum_features"] = "handled_correctly"
            
            # Test database connection failure
            try:
                # Temporarily corrupt database path
                original_url = settings.database_url
                settings.database_url = "invalid://path"
                await init_database()
                error_tests["database_connection"] = "unexpected_success"
            except Exception as e:
                error_tests["database_connection"] = "handled_correctly"
            finally:
                settings.database_url = original_url
            
            self.results["error_handling"] = {
                "status": "success",
                "tests": error_tests,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Error handling testing completed")
            
        except Exception as e:
            logger.error("Error handling test failed", error=str(e))
            self.results["error_handling"] = {
                "status": "failed",
                "error": str(e)
            }
    
    async def generate_report(self):
        """Generate comprehensive test report."""
        logger.info("Generating test report")
        
        # Calculate summary statistics
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for category, tests in self.results.items():
            if isinstance(tests, dict) and "status" in tests:
                total_tests += 1
                if tests["status"] == "success":
                    passed_tests += 1
                else:
                    failed_tests += 1
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "total_duration": self.results["total_duration"],
            "total_errors": len(self.results["errors"])
        }
        
        self.results["summary"] = summary
        
        # Save report to file
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # Print summary
        print("\n" + "="*80)
        print("COMPREHENSIVE SYSTEM TEST REPORT")
        print("="*80)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed_tests']}")
        print(f"Failed: {summary['failed_tests']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Total Duration: {summary['total_duration']:.2f} seconds")
        print(f"Total Errors: {summary['total_errors']}")
        print(f"Report saved to: {report_file}")
        print("="*80)
        
        # Print detailed results
        for category, result in self.results.items():
            if category not in ["summary", "test_start", "test_end", "total_duration", "errors"]:
                print(f"\n{category.upper()}:")
                if isinstance(result, dict):
                    for key, value in result.items():
                        if isinstance(value, dict) and "status" in value:
                            status = value["status"]
                            print(f"  {key}: {status}")
                        elif key == "status":
                            print(f"  Overall: {value}")
        
        if self.results["errors"]:
            print(f"\nERRORS ({len(self.results['errors'])}):")
            for error in self.results["errors"]:
                print(f"  {error['test']}: {error['error']}")
        
        logger.info("Test report generated", report_file=report_file)

async def main():
    """Run comprehensive system tests."""
    tester = ComprehensiveSystemTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())