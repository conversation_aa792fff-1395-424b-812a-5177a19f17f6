# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

import ccxt.async_support
from ccxt.async_support.base.ws.cache import ArrayCache, ArrayCacheBySymbolById, ArrayCacheByTimestamp
import hashlib
from ccxt.base.types import Any, Balances, Int, Num, Order, OrderBook, OrderSide, OrderType, Str, Strings, Ticker, Tickers, Trade, TradingFees
from ccxt.async_support.base.ws.client import Client
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import ArgumentsRequired


class bitvavo(ccxt.async_support.bitvavo):

    def describe(self) -> Any:
        return self.deep_extend(super(bitvavo, self).describe(), {
            'has': {
                'ws': True,
                'cancelOrdersWs': False,
                'fetchTradesWs': False,
                'watchOrderBook': True,
                'watchTrades': True,
                'watchTicker': True,
                'watchTickers': True,
                'watchBidsAsks': True,
                'watchOHLCV': True,
                'watchOrders': True,
                'watchMyTrades': True,
                'cancelAllOrdersWs': True,
                'cancelOrderWs': True,
                'createOrderWs': True,
                'createStopLimitOrderWs': True,
                'createStopMarketOrderWs': True,
                'createStopOrderWs': True,
                'editOrderWs': True,
                'fetchBalanceWs': True,
                'fetchCurrenciesWS': True,
                'fetchDepositAddressWs': True,
                'fetchDepositsWs': True,
                'fetchDepositWithdrawFeesWs': True,
                'fetchMyTradesWs': True,
                'fetchOHLCVWs': True,
                'fetchOpenOrdersWs': True,
                'fetchOrderWs': True,
                'fetchOrderBookWs': True,
                'fetchOrdersWs': True,
                'fetchTickerWs': True,
                'fetchTickersWs': True,
                'fetchTimeWs': True,
                'fetchTradingFeesWs': True,
                'fetchWithdrawalsWs': True,
                'withdrawWs': True,
            },
            'urls': {
                'api': {
                    'ws': 'wss://ws.bitvavo.com/v2',
                },
            },
            'options': {
                'supressMultipleWsRequestsError': False,  # if True, will not raise an error when using the same messageHash for more than one request. By making False you may receive responses from different requests on the same action
                'tradesLimit': 1000,
                'ordersLimit': 1000,
                'OHLCVLimit': 1000,
            },
        })

    async def watch_public(self, name, symbol, params={}):
        await self.load_markets()
        market = self.market(symbol)
        messageHash = name + '@' + market['id']
        url = self.urls['api']['ws']
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': name,
                    'markets': [
                        market['id'],
                    ],
                },
            ],
        }
        message = self.extend(request, params)
        return await self.watch(url, messageHash, message, messageHash)

    async def watch_public_multiple(self, methodName, channelName: str, symbols, params={}):
        await self.load_markets()
        symbols = self.market_symbols(symbols)
        messageHashes = [methodName]
        args = []
        for i in range(0, len(symbols)):
            market = self.market(symbols[i])
            args.append(market['id'])
        url = self.urls['api']['ws']
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': channelName,
                    'markets': args,
                },
            ],
        }
        message = self.extend(request, params)
        return await self.watch_multiple(url, messageHashes, message, messageHashes)

    async def watch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://docs.bitvavo.com/#tag/Market-data-subscription-WebSocket/paths/~1subscribeTicker24h/post

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        return await self.watch_public('ticker24h', symbol, params)

    async def watch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for all markets of a specific list

        https://docs.bitvavo.com/#tag/Market-data-subscription-WebSocket/paths/~1subscribeTicker24h/post

        :param str[] [symbols]: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols, None, False)
        channel = 'ticker24h'
        tickers = await self.watch_public_multiple(channel, channel, symbols, params)
        return self.filter_by_array(tickers, 'symbol', symbols)

    def handle_ticker(self, client: Client, message):
        #
        #     {
        #         "event": "ticker24h",
        #         "data": [
        #             {
        #                 "market": "ETH-EUR",
        #                 "open": "193.5",
        #                 "high": "202.72",
        #                 "low": "192.46",
        #                 "last": "199.01",
        #                 "volume": "3587.05020246",
        #                 "volumeQuote": "708030.17",
        #                 "bid": "199.56",
        #                 "bidSize": "4.14730802",
        #                 "ask": "199.57",
        #                 "askSize": "6.13642074",
        #                 "timestamp": 1590770885217
        #             }
        #         ]
        #     }
        #
        self.handle_bid_ask(client, message)
        event = self.safe_string(message, 'event')
        tickers = self.safe_value(message, 'data', [])
        result = []
        for i in range(0, len(tickers)):
            data = tickers[i]
            marketId = self.safe_string(data, 'market')
            market = self.safe_market(marketId, None, '-')
            messageHash = event + '@' + marketId
            ticker = self.parse_ticker(data, market)
            symbol = ticker['symbol']
            self.tickers[symbol] = ticker
            result.append(ticker)
            client.resolve(ticker, messageHash)
        client.resolve(result, event)

    async def watch_bids_asks(self, symbols: Strings = None, params={}) -> Tickers:
        """
        watches best bid & ask for symbols

        https://docs.bitvavo.com/#tag/Market-data-subscription-WebSocket/paths/~1subscribeTicker24h/post

        :param str[] symbols: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols, None, False)
        channel = 'ticker24h'
        tickers = await self.watch_public_multiple('bidask', channel, symbols, params)
        return self.filter_by_array(tickers, 'symbol', symbols)

    def handle_bid_ask(self, client: Client, message):
        event = 'bidask'
        tickers = self.safe_value(message, 'data', [])
        result = []
        for i in range(0, len(tickers)):
            data = tickers[i]
            ticker = self.parse_ws_bid_ask(data)
            symbol = ticker['symbol']
            self.bidsasks[symbol] = ticker
            result.append(ticker)
            messageHash = event + ':' + symbol
            client.resolve(ticker, messageHash)
        client.resolve(result, event)

    def parse_ws_bid_ask(self, ticker, market=None):
        marketId = self.safe_string(ticker, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = self.safe_string(market, 'symbol')
        timestamp = self.safe_integer(ticker, 'timestamp')
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'ask': self.safe_number(ticker, 'ask'),
            'askVolume': self.safe_number(ticker, 'askSize'),
            'bid': self.safe_number(ticker, 'bid'),
            'bidVolume': self.safe_number(ticker, 'bidSize'),
            'info': ticker,
        }, market)

    async def watch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        symbol = self.symbol(symbol)
        trades = await self.watch_public('trades', symbol, params)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_since_limit(trades, since, limit, 'timestamp', True)

    def handle_trade(self, client: Client, message):
        #
        #     {
        #         "event": "trade",
        #         "timestamp": 1590779594547,
        #         "market": "ETH-EUR",
        #         "id": "450c3298-f082-4461-9e2c-a0262cc7cc2e",
        #         "amount": "0.05026233",
        #         "price": "198.46",
        #         "side": "buy"
        #     }
        #
        marketId = self.safe_string(message, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = market['symbol']
        name = 'trades'
        messageHash = name + '@' + marketId
        trade = self.parse_trade(message, market)
        tradesArray = self.safe_value(self.trades, symbol)
        if tradesArray is None:
            limit = self.safe_integer(self.options, 'tradesLimit', 1000)
            tradesArray = ArrayCache(limit)
        tradesArray.append(trade)
        self.trades[symbol] = tradesArray
        client.resolve(tradesArray, messageHash)

    async def watch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        watches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        name = 'candles'
        marketId = market['id']
        interval = self.safe_string(self.timeframes, timeframe, timeframe)
        messageHash = name + '@' + marketId + '_' + interval
        url = self.urls['api']['ws']
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': 'candles',
                    'interval': [interval],
                    'markets': [marketId],
                },
            ],
        }
        message = self.extend(request, params)
        ohlcv = await self.watch(url, messageHash, message, messageHash)
        if self.newUpdates:
            limit = ohlcv.getLimit(symbol, limit)
        return self.filter_by_since_limit(ohlcv, since, limit, 0, True)

    def handle_fetch_ohlcv(self, client: Client, message):
        #
        #    {
        #        action: 'getCandles',
        #        response: [
        #            [1690325820000, '26453', '26453', '26436', '26447', '0.01626246'],
        #            [1690325760000, '26454', '26454', '26453', '26453', '0.00037707']
        #        ]
        #    }
        #
        response = self.safe_value(message, 'response')
        ohlcv = self.parse_ohlcvs(response, None, None, None)
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(ohlcv, messageHash)

    def handle_ohlcv(self, client: Client, message):
        #
        #     {
        #         "event": "candle",
        #         "market": "BTC-EUR",
        #         "interval": "1m",
        #         "candle": [
        #             [
        #                 1590797160000,
        #                 "8480.9",
        #                 "8480.9",
        #                 "8480.9",
        #                 "8480.9",
        #                 "0.01038628"
        #             ]
        #         ]
        #     }
        #
        name = 'candles'
        marketId = self.safe_string(message, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = market['symbol']
        interval = self.safe_string(message, 'interval')
        # use a reverse lookup in a static map instead
        timeframe = self.find_timeframe(interval)
        messageHash = name + '@' + marketId + '_' + interval
        candles = self.safe_value(message, 'candle')
        self.ohlcvs[symbol] = self.safe_value(self.ohlcvs, symbol, {})
        stored = self.safe_value(self.ohlcvs[symbol], timeframe)
        if stored is None:
            limit = self.safe_integer(self.options, 'OHLCVLimit', 1000)
            stored = ArrayCacheByTimestamp(limit)
            self.ohlcvs[symbol][timeframe] = stored
        for i in range(0, len(candles)):
            candle = candles[i]
            parsed = self.parse_ohlcv(candle, market)
            stored.append(parsed)
        client.resolve(stored, messageHash)

    async def watch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        watches information on open orders with bid(buy) and ask(sell) prices, volumes and other data
        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        name = 'book'
        messageHash = name + '@' + market['id']
        url = self.urls['api']['ws']
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': name,
                    'markets': [
                        market['id'],
                    ],
                },
            ],
        }
        subscription: dict = {
            'messageHash': messageHash,
            'name': name,
            'symbol': symbol,
            'marketId': market['id'],
            'method': self.handle_order_book_subscription,
            'limit': limit,
            'params': params,
        }
        message = self.extend(request, params)
        orderbook = await self.watch(url, messageHash, message, messageHash, subscription)
        return orderbook.limit()

    def handle_delta(self, bookside, delta):
        price = self.safe_float(delta, 0)
        amount = self.safe_float(delta, 1)
        bookside.store(price, amount)

    def handle_deltas(self, bookside, deltas):
        for i in range(0, len(deltas)):
            self.handle_delta(bookside, deltas[i])

    def handle_order_book_message(self, client: Client, message, orderbook):
        #
        #     {
        #         "event": "book",
        #         "market": "BTC-EUR",
        #         "nonce": 36947383,
        #         "bids": [
        #             ["8477.8", "0"]
        #         ],
        #         "asks": [
        #             ["8550.9", "0"]
        #         ]
        #     }
        #
        nonce = self.safe_integer(message, 'nonce')
        if nonce > orderbook['nonce']:
            self.handle_deltas(orderbook['asks'], self.safe_value(message, 'asks', []))
            self.handle_deltas(orderbook['bids'], self.safe_value(message, 'bids', []))
            orderbook['nonce'] = nonce
        return orderbook

    def handle_order_book(self, client: Client, message):
        #
        #     {
        #         "event": "book",
        #         "market": "BTC-EUR",
        #         "nonce": 36729561,
        #         "bids": [
        #             ["8513.3", "0"],
        #             ['8518.8', "0.64236203"],
        #             ['8513.6', "0.32435481"],
        #         ],
        #         "asks": []
        #     }
        #
        event = self.safe_string(message, 'event')
        marketId = self.safe_string(message, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = market['symbol']
        messageHash = event + '@' + market['id']
        orderbook = self.safe_value(self.orderbooks, symbol)
        if orderbook is None:
            return
        if orderbook['nonce'] is None:
            subscription = self.safe_value(client.subscriptions, messageHash, {})
            watchingOrderBookSnapshot = self.safe_value(subscription, 'watchingOrderBookSnapshot')
            if watchingOrderBookSnapshot is None:
                subscription['watchingOrderBookSnapshot'] = True
                client.subscriptions[messageHash] = subscription
                options = self.safe_value(self.options, 'watchOrderBookSnapshot', {})
                delay = self.safe_integer(options, 'delay', self.rateLimit)
                # fetch the snapshot in a separate async call after a warmup delay
                self.delay(delay, self.watch_order_book_snapshot, client, message, subscription)
            orderbook.cache.append(message)
        else:
            self.handle_order_book_message(client, message, orderbook)
            client.resolve(orderbook, messageHash)

    async def watch_order_book_snapshot(self, client, message, subscription):
        params = self.safe_value(subscription, 'params')
        marketId = self.safe_string(subscription, 'marketId')
        name = 'getBook'
        messageHash = name + '@' + marketId
        url = self.urls['api']['ws']
        request: dict = {
            'action': name,
            'market': marketId,
        }
        orderbook = await self.watch(url, messageHash, self.extend(request, params), messageHash, subscription)
        return orderbook.limit()

    def handle_order_book_snapshot(self, client: Client, message):
        #
        #     {
        #         "action": "getBook",
        #         "response": {
        #             "market": "BTC-EUR",
        #             "nonce": 36946120,
        #             "bids": [
        #                 ['8494.9', "0.24399521"],
        #                 ['8494.8', "0.34884085"],
        #                 ['8493.9', "0.14535128"],
        #             ],
        #             "asks": [
        #                 ["8495", "0.46982463"],
        #                 ['8495.1', "0.12178267"],
        #                 ['8496.2', "0.21924143"],
        #             ]
        #         }
        #     }
        #
        response = self.safe_value(message, 'response')
        if response is None:
            return
        marketId = self.safe_string(response, 'market')
        symbol = self.safe_symbol(marketId, None, '-')
        name = 'book'
        messageHash = name + '@' + marketId
        orderbook = self.orderbooks[symbol]
        snapshot = self.parse_order_book(response, symbol)
        snapshot['nonce'] = self.safe_integer(response, 'nonce')
        orderbook.reset(snapshot)
        # unroll the accumulated deltas
        messages = orderbook.cache
        for i in range(0, len(messages)):
            messageItem = messages[i]
            self.handle_order_book_message(client, messageItem, orderbook)
        self.orderbooks[symbol] = orderbook
        client.resolve(orderbook, messageHash)

    def handle_order_book_subscription(self, client: Client, message, subscription):
        symbol = self.safe_string(subscription, 'symbol')
        limit = self.safe_integer(subscription, 'limit')
        if symbol in self.orderbooks:
            del self.orderbooks[symbol]
        self.orderbooks[symbol] = self.order_book({}, limit)

    def handle_order_book_subscriptions(self, client: Client, message, marketIds):
        name = 'book'
        for i in range(0, len(marketIds)):
            marketId = self.safe_string(marketIds, i)
            symbol = self.safe_symbol(marketId, None, '-')
            messageHash = name + '@' + marketId
            if not (symbol in self.orderbooks):
                subscription = self.safe_value(client.subscriptions, messageHash)
                method = self.safe_value(subscription, 'method')
                if method is not None:
                    method(client, message, subscription)

    async def watch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        watches information on multiple orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' watchOrders() requires a symbol argument')
        await self.load_markets()
        await self.authenticate()
        market = self.market(symbol)
        symbol = market['symbol']
        marketId = market['id']
        url = self.urls['api']['ws']
        name = 'account'
        messageHash = 'order:' + symbol
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': name,
                    'markets': [marketId],
                },
            ],
        }
        orders = await self.watch(url, messageHash, request, messageHash)
        if self.newUpdates:
            limit = orders.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(orders, symbol, since, limit, True)

    async def watch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        watches information on multiple trades made by the user
        :param str symbol: unified market symbol of the market trades were made in
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trade structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' watchMyTrades() requires a symbol argument')
        await self.load_markets()
        await self.authenticate()
        market = self.market(symbol)
        symbol = market['symbol']
        marketId = market['id']
        url = self.urls['api']['ws']
        name = 'account'
        messageHash = 'myTrades:' + symbol
        request: dict = {
            'action': 'subscribe',
            'channels': [
                {
                    'name': name,
                    'markets': [marketId],
                },
            ],
        }
        trades = await self.watch(url, messageHash, request, messageHash)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(trades, symbol, since, limit, True)

    async def create_order_ws(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}) -> Order:
        """
        create a trade order

        https://docs.bitvavo.com/#tag/Orders/paths/~1order/post

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float price: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :param str [params.timeInForce]: "GTC", "IOC", or "PO"
        :param float [params.stopPrice]: The price at which a trigger order is triggered at
        :param float [params.triggerPrice]: The price at which a trigger order is triggered at
        :param bool [params.postOnly]: If True, the order will only be posted to the order book and not executed immediately
        :param float [params.stopLossPrice]: The price at which a stop loss order is triggered at
        :param float [params.takeProfitPrice]: The price at which a take profit order is triggered at
        :param str [params.triggerType]: "price"
        :param str [params.triggerReference]: "lastTrade", "bestBid", "bestAsk", "midPrice" Only for stop orders: Use self to determine which parameter will trigger the order
        :param str [params.selfTradePrevention]: "decrementAndCancel", "cancelOldest", "cancelNewest", "cancelBoth"
        :param bool [params.disableMarketProtection]: don't cancel if the next fill price is 10% worse than the best fill price
        :param bool [params.responseRequired]: Set self to 'false' when only an acknowledgement of success or failure is required, self is faster.
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request = self.create_order_request(symbol, type, side, amount, price, params)
        return await self.watch_request('privateCreateOrder', request)

    async def edit_order_ws(self, id: str, symbol: str, type: OrderType, side: OrderSide, amount: Num = None, price: Num = None, params={}) -> Order:
        """
        edit a trade order

        https://docs.bitvavo.com/#tag/Orders/paths/~1order/put

        :param str id: cancel order id
        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float [amount]: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request = self.edit_order_request(id, symbol, type, side, amount, price, params)
        return await self.watch_request('privateUpdateOrder', request)

    async def cancel_order_ws(self, id: str, symbol: Str = None, params={}):
        """

        https://docs.bitvavo.com/#tag/Orders/paths/~1order/delete

        cancels an open order
        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request = self.cancel_order_request(id, symbol, params)
        return await self.watch_request('privateCancelOrder', request)

    async def cancel_all_orders_ws(self, symbol: Str = None, params={}):
        """

        https://docs.bitvavo.com/#tag/Orders/paths/~1orders/delete

        cancel all open orders
        :param str symbol: unified market symbol, only orders in the market of self symbol are cancelled when symbol is not None
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['market'] = market['id']
        return await self.watch_request('privateCancelOrders', self.extend(request, params))

    def handle_multiple_orders(self, client: Client, message):
        #
        #    {
        #        action: 'privateCancelOrders',
        #        response: [{
        #            orderId: 'd71df826-1130-478a-8741-d219128675b0'
        #        }]
        #    }
        #
        # action = self.safe_string(message, 'action')
        response = self.safe_list(message, 'response')
        # firstRawOrder = self.safe_value(response, 0, {})
        # marketId = self.safe_string(firstRawOrder, 'market')
        orders = self.parse_orders(response)
        # messageHash = self.build_message_hash(action, {'market': marketId})
        # client.resolve(orders, messageHash)
        # messageHash = self.build_message_hash(action, message)
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(orders, messageHash)

    async def fetch_order_ws(self, id: str, symbol: Str = None, params={}) -> Order:
        """

        https://docs.bitvavo.com/#tag/General/paths/~1assets/get

        fetches information on an order made by the user
        :param str id: the order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchOrder() requires a symbol argument')
        await self.load_markets()
        await self.authenticate()
        market = self.market(symbol)
        request: dict = {
            'orderId': id,
            'market': market['id'],
        }
        return await self.watch_request('privateGetOrder', self.extend(request, params))

    async def fetch_orders_ws(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """

        https://docs.bitvavo.com/#tag/Orders/paths/~1orders/get

        fetches information on multiple orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of  orde structures to retrieve
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchOrdersWs() requires a symbol argument')
        await self.load_markets()
        await self.authenticate()
        request = self.fetchOrdersRequest(symbol, since, limit, params)
        orders = await self.watch_request('privateGetOrders', request)
        return self.filter_by_symbol_since_limit(orders, symbol, since, limit)

    def request_id(self):
        ts = str(self.milliseconds())
        randomNumber = self.rand_number(4)
        randomPart = str(randomNumber)
        return int(ts + randomPart)

    async def watch_request(self, action, request):
        messageHash = self.request_id()
        messageHashStr = str(messageHash)
        request['action'] = action
        request['requestId'] = messageHash
        url = self.urls['api']['ws']
        return await self.watch(url, messageHashStr, request, messageHashStr)

    async def fetch_open_orders_ws(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request: dict = {
            # 'market': market['id'],  # rate limit 25 without a market, 1 with market specified
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['market'] = market['id']
        orders = await self.watch_request('privateGetOrdersOpen', self.extend(request, params))
        return self.filter_by_symbol_since_limit(orders, symbol, since, limit)

    async def fetch_my_trades_ws(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """

        https://docs.bitvavo.com/#tag/Trades

        fetch all trades made by the user
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchMyTradesWs() requires a symbol argument')
        await self.load_markets()
        await self.authenticate()
        request = self.fetchMyTradesRequest(symbol, since, limit, params)
        myTrades = await self.watch_request('privateGetTrades', request)
        return self.filter_by_symbol_since_limit(myTrades, symbol, since, limit)

    def handle_my_trades(self, client: Client, message):
        #
        #    {
        #        action: 'privateGetTrades',
        #        response: [
        #            {
        #                "id": "108c3633-0276-4480-a902-17a01829deae",
        #                "orderId": "1d671998-3d44-4df4-965f-0d48bd129a1b",
        #                "timestamp": 1542967486256,
        #                "market": "BTC-EUR",
        #                "side": "buy",
        #                "amount": "0.005",
        #                "price": "5000.1",
        #                "taker": True,
        #                "fee": "0.03",
        #                "feeCurrency": "EUR",
        #                "settled": True
        #            }
        #        ]
        #    }
        #
        #
        # action = self.safe_string(message, 'action')
        response = self.safe_list(message, 'response')
        # marketId = self.safe_string(firstRawTrade, 'market')
        trades = self.parse_trades(response, None, None, None)
        # messageHash = self.build_message_hash(action, {'market': marketId})
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(trades, messageHash)

    async def withdraw_ws(self, code: str, amount, address, tag=None, params={}):
        """
        make a withdrawal
        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        self.check_address(address)
        await self.load_markets()
        await self.authenticate()
        request = self.withdrawRequest(code, amount, address, tag, params)
        return await self.watch_request('privateWithdrawAssets', request)

    def handle_withdraw(self, client: Client, message):
        #
        #    {
        #        action: 'privateWithdrawAssets',
        #        response: {
        #         "success": True,
        #         "symbol": "BTC",
        #         "amount": "1.5"
        #        }
        #    }
        #
        # action = self.safe_string(message, 'action')
        # messageHash = self.build_message_hash(action, message)
        messageHash = self.safe_string(message, 'requestId')
        response = self.safe_value(message, 'response')
        withdraw = self.parse_transaction(response)
        client.resolve(withdraw, messageHash)

    async def fetch_withdrawals_ws(self, code: Str = None, since: Int = None, limit: Int = None, params={}):
        """

        https://docs.bitvavo.com/#tag/Account/paths/~1withdrawalHistory/get

        fetch all withdrawals made from an account
        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request = self.fetchWithdrawalsRequest(code, since, limit, params)
        withdraws = await self.watch_request('privateGetWithdrawalHistory', request)
        return self.filter_by_currency_since_limit(withdraws, code, since, limit)

    def handle_withdraws(self, client: Client, message):
        #
        #    {
        #        action: 'privateGetWithdrawalHistory',
        #        response: [{
        #                timestamp: *************,
        #                symbol: 'BTC',
        #                amount: '0.0009',
        #                fee: '0',
        #                status: 'completed',
        #                txId: '7dbadc658d7d59c129de1332c55ee8e08d0ab74432faae03b417b9809c819d1f'
        #            },
        #            ...
        #        ]
        #    }
        #
        # action = self.safe_string(message, 'action')
        # messageHash = self.build_message_hash(action, message)
        response = self.safe_list(message, 'response')
        messageHash = self.safe_string(message, 'requestId')
        withdrawals = self.parse_transactions(response, None, None, None, {'type': 'withdrawal'})
        client.resolve(withdrawals, messageHash)

    async def fetch_ohlcv_ws(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """

        https://docs.bitvavo.com/#tag/Market-Data/paths/~1{market}~1candles/get

        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        request = self.fetchOHLCVRequest(symbol, timeframe, since, limit, params)
        action = 'getCandles'
        ohlcv = await self.watch_request(action, request)
        return self.filter_by_since_limit(ohlcv, since, limit, 0, True)

    async def fetch_deposits_ws(self, code: Str = None, since: Int = None, limit: Int = None, params={}):
        """

        https://docs.bitvavo.com/#tag/Account/paths/~1depositHistory/get

        fetch all deposits made to an account
        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        request = self.fetchDepositsRequest(code, since, limit, params)
        deposits = await self.watch_request('privateGetDepositHistory', request)
        return self.filter_by_currency_since_limit(deposits, code, since, limit)

    def handle_deposits(self, client: Client, message):
        #
        #    {
        #        action: 'privateGetDepositHistory',
        #        response: [{
        #                timestamp: *************,
        #                symbol: 'BTC',
        #                amount: '0.0009',
        #                fee: '0',
        #                status: 'completed',
        #                txId: '7dbadc658d7d59c129de1332c55ee8e08d0ab74432faae03b417b9809c819d1f'
        #            },
        #            ...
        #        ]
        #    }
        #
        response = self.safe_value(message, 'response')
        deposits = self.parse_transactions(response, None, None, None, {'type': 'deposit'})
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(deposits, messageHash)

    async def fetch_trading_fees_ws(self, params={}) -> TradingFees:
        """

        https://docs.bitvavo.com/#tag/Account/paths/~1account/get

        fetch the trading fees for multiple markets
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: a dictionary of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>` indexed by market symbols
        """
        await self.load_markets()
        await self.authenticate()
        return await self.watch_request('privateGetAccount', params)

    async def fetch_markets_ws(self, params={}):
        """

        https://docs.bitvavo.com/#tag/General/paths/~1markets/get

        retrieves data on all markets for bitvavo
        :param dict [params]: extra parameters specific to the exchange api endpoint
        :returns dict[]: an array of objects representing market data
        """
        return await self.watch_request('getMarkets', params)

    async def fetch_currencies_ws(self, params={}):
        """

        https://docs.bitvavo.com/#tag/General/paths/~1assets/get

        fetches all available currencies on an exchange
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: an associative dictionary of currencies
        """
        await self.load_markets()
        return await self.watch_request('getAssets', params)

    def handle_fetch_currencies(self, client: Client, message):
        #
        #    {
        #        action: 'getAssets',
        #        response: [{
        #                symbol: '1INCH',
        #                name: '1inch',
        #                decimals: 8,
        #                depositFee: '0',
        #                depositConfirmations: 64,
        #                depositStatus: 'OK',
        #                withdrawalFee: '13',
        #                withdrawalMinAmount: '13',
        #                withdrawalStatus: 'OK',
        #                networks: [Array],
        #                message: ''
        #            },
        #            ...
        #        ]
        #    }
        #
        messageHash = self.safe_string(message, 'requestId')
        response = self.safe_value(message, 'response')
        currencies = self.parse_currencies(response)
        client.resolve(currencies, messageHash)

    def handle_trading_fees(self, client, message):
        #
        #    {
        #        action: 'privateGetAccount',
        #        response: {
        #            fees: {
        #                taker: '0.0025',
        #                maker: '0.0015',
        #                volume: '1693.74'
        #            }
        #        }
        #    }
        #
        messageHash = self.safe_string(message, 'requestId')
        response = self.safe_value(message, 'response')
        fees = self.parse_trading_fees(response)
        client.resolve(fees, messageHash)

    async def fetch_balance_ws(self, params={}) -> Balances:
        """

        https://docs.bitvavo.com/#tag/Account/paths/~1balance/get

        query for balance and get the amount of funds available for trading or funds locked in orders
        :param dict [params]: extra parameters specific to the bitvavo api endpoint
        :returns dict: a `balance structure <https://docs.ccxt.com/en/latest/manual.html?#balance-structure>`
        """
        await self.load_markets()
        await self.authenticate()
        return await self.watch_request('privateGetBalance', params)

    def handle_fetch_balance(self, client: Client, message):
        #
        #    {
        #        action: 'privateGetBalance',
        #        response: [{
        #                symbol: 'ADA',
        #                available: '0',
        #                inOrder: '0'
        #            },
        #            ...
        #        ]
        #    }
        #
        messageHash = self.safe_string(message, 'requestId')
        response = self.safe_value(message, 'response', [])
        balance = self.parse_balance(response)
        client.resolve(balance, messageHash)

    def handle_single_order(self, client: Client, message):
        #
        #    {
        #        action: 'privateCreateOrder',
        #        response: {
        #            orderId: 'd71df826-1130-478a-8741-d219128675b0',
        #            market: 'BTC-EUR',
        #            created: 1689792749748,
        #            updated: 1689792749748,
        #            status: 'new',
        #            side: 'sell',
        #            orderType: 'limit',
        #            amount: '0.0002',
        #            amountRemaining: '0.0002',
        #            price: '37000',
        #            onHold: '0.0002',
        #            onHoldCurrency: 'BTC',
        #            filledAmount: '0',
        #            filledAmountQuote: '0',
        #            feePaid: '0',
        #            feeCurrency: 'EUR',
        #            fills: [],
        #            selfTradePrevention: 'decrementAndCancel',
        #            visible: True,
        #            timeInForce: 'GTC',
        #            postOnly: False
        #        }
        #    }
        #
        response = self.safe_value(message, 'response', {})
        order = self.parse_order(response)
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(order, messageHash)

    def handle_markets(self, client: Client, message):
        #
        #    {
        #        action: 'getMarkets',
        #        response: [{
        #                market: '1INCH-EUR',
        #                status: 'trading',
        #                base: '1INCH',
        #                quote: 'EUR',
        #                pricePrecision: 5,
        #                minOrderInBaseAsset: '2',
        #                minOrderInQuoteAsset: '5',
        #                maxOrderInBaseAsset: '1000000000',
        #                maxOrderInQuoteAsset: '1000000000',
        #                orderTypes: [Array]
        #            },
        #            ...
        #        ]
        #    }
        #
        response = self.safe_value(message, 'response', {})
        markets = self.parse_markets(response)
        messageHash = self.safe_string(message, 'requestId')
        client.resolve(markets, messageHash)

    def build_message_hash(self, action, params={}):
        methods: dict = {
            'privateCreateOrder': self.action_and_market_message_hash,
            'privateUpdateOrder': self.action_and_order_id_message_hash,
            'privateCancelOrder': self.action_and_order_id_message_hash,
            'privateGetOrder': self.action_and_order_id_message_hash,
            'privateGetTrades': self.action_and_market_message_hash,
        }
        method = self.safe_value(methods, action)
        messageHash = action
        if method is not None:
            messageHash = method(action, params)
        return messageHash

    def action_and_market_message_hash(self, action, params={}):
        symbol = self.safe_string(params, 'market', '')
        return action + symbol

    def action_and_order_id_message_hash(self, action, params={}):
        orderId = self.safe_string(params, 'orderId')
        if orderId is None:
            raise ExchangeError(self.id + ' privateUpdateOrderMessageHash requires a orderId parameter')
        return action + orderId

    def handle_order(self, client: Client, message):
        #
        #     {
        #         "event": "order",
        #         "orderId": "f0e5180f-9497-4d05-9dc2-7056e8a2de9b",
        #         "market": "ETH-EUR",
        #         "created": 1590948500319,
        #         "updated": 1590948500319,
        #         "status": "new",
        #         "side": "sell",
        #         "orderType": "limit",
        #         "amount": "0.1",
        #         "amountRemaining": "0.1",
        #         "price": "300",
        #         "onHold": "0.1",
        #         "onHoldCurrency": "ETH",
        #         "selfTradePrevention": "decrementAndCancel",
        #         "visible": True,
        #         "timeInForce": "GTC",
        #         "postOnly": False
        #     }
        #
        marketId = self.safe_string(message, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = market['symbol']
        messageHash = 'order:' + symbol
        order = self.parse_order(message, market)
        if self.orders is None:
            limit = self.safe_integer(self.options, 'ordersLimit', 1000)
            self.orders = ArrayCacheBySymbolById(limit)
        orders = self.orders
        orders.append(order)
        client.resolve(self.orders, messageHash)

    def handle_my_trade(self, client: Client, message):
        #
        #     {
        #         "event": "fill",
        #         "timestamp": 1590964470132,
        #         "market": "ETH-EUR",
        #         "orderId": "85d082e1-eda4-4209-9580-248281a29a9a",
        #         "fillId": "861d2da5-aa93-475c-8d9a-dce431bd4211",
        #         "side": "sell",
        #         "amount": "0.1",
        #         "price": "211.46",
        #         "taker": True,
        #         "fee": "0.056",
        #         "feeCurrency": "EUR"
        #     }
        #
        marketId = self.safe_string(message, 'market')
        market = self.safe_market(marketId, None, '-')
        symbol = market['symbol']
        messageHash = 'myTrades:' + symbol
        trade = self.parse_trade(message, market)
        if self.myTrades is None:
            limit = self.safe_integer(self.options, 'tradesLimit', 1000)
            self.myTrades = ArrayCache(limit)
        tradesArray = self.myTrades
        tradesArray.append(trade)
        client.resolve(tradesArray, messageHash)

    def handle_subscription_status(self, client: Client, message):
        #
        #     {
        #         "event": "subscribed",
        #         "subscriptions": {
        #             "book": ["BTC-EUR"]
        #         }
        #     }
        #
        subscriptions = self.safe_value(message, 'subscriptions', {})
        methods: dict = {
            'book': self.handle_order_book_subscriptions,
        }
        names = list(subscriptions.keys())
        for i in range(0, len(names)):
            name = names[i]
            method = self.safe_value(methods, name)
            if method is not None:
                subscription = self.safe_value(subscriptions, name)
                method(client, message, subscription)
        return message

    async def authenticate(self, params={}):
        url = self.urls['api']['ws']
        client = self.client(url)
        messageHash = 'authenticated'
        future = self.safe_value(client.subscriptions, messageHash)
        if future is None:
            timestamp = self.milliseconds()
            stringTimestamp = str(timestamp)
            auth = stringTimestamp + 'GET/' + self.version + '/websocket'
            signature = self.hmac(self.encode(auth), self.encode(self.secret), hashlib.sha256)
            action = 'authenticate'
            request: dict = {
                'action': action,
                'key': self.apiKey,
                'signature': signature,
                'timestamp': timestamp,
            }
            message = self.extend(request, params)
            future = await self.watch(url, messageHash, message, messageHash)
            client.subscriptions[messageHash] = future
        return future

    def handle_authentication_message(self, client: Client, message):
        #
        #     {
        #         "event": "authenticate",
        #         "authenticated": True
        #     }
        #
        messageHash = 'authenticated'
        authenticated = self.safe_bool(message, 'authenticated', False)
        if authenticated:
            # we resolve the future here permanently so authentication only happens once
            client.resolve(message, messageHash)
        else:
            error = AuthenticationError(self.json(message))
            client.reject(error, messageHash)
            # allows further authentication attempts
            if messageHash in client.subscriptions:
                del client.subscriptions[messageHash]

    def handle_error_message(self, client: Client, message):
        #
        #    {
        #        action: 'privateCreateOrder',
        #        market: 'BTC-EUR',
        #        errorCode: 217,
        #        error: 'Minimum order size in quote currency is 5 EUR or 0.001 BTC.'
        #    }
        #    {
        #        action: 'privateCreateOrder',
        #        requestId: '17317539426571916',
        #        market: 'USDT-EUR',
        #        errorCode: 216,
        #        error: 'You do not have sufficient balance to complete self operation.'
        #    }
        #
        error = self.safe_string(message, 'error')
        code = self.safe_integer(error, 'errorCode')
        action = self.safe_string(message, 'action')
        buildMessage = self.build_message_hash(action, message)
        messageHash = self.safe_string(message, 'requestId', buildMessage)
        rejected = False
        try:
            self.handle_errors(code, error, client.url, None, None, error, message, None, None)
        except Exception as e:
            rejected = True
            client.reject(e, messageHash)
        if not rejected:
            client.reject(message, messageHash)

    def handle_message(self, client: Client, message):
        #
        #     {
        #         "event": "subscribed",
        #         "subscriptions": {
        #             "book": ["BTC-EUR"]
        #         }
        #     }
        #
        #     {
        #         "event": "book",
        #         "market": "BTC-EUR",
        #         "nonce": 36729561,
        #         "bids": [
        #             ["8513.3", "0"],
        #             ['8518.8', "0.64236203"],
        #             ['8513.6', "0.32435481"],
        #         ],
        #         "asks": []
        #     }
        #
        #     {
        #         "action": "getBook",
        #         "response": {
        #             "market": "BTC-EUR",
        #             "nonce": 36946120,
        #             "bids": [
        #                 ['8494.9', "0.24399521"],
        #                 ['8494.8', "0.34884085"],
        #                 ['8493.9', "0.14535128"],
        #             ],
        #             "asks": [
        #                 ["8495", "0.46982463"],
        #                 ['8495.1', "0.12178267"],
        #                 ['8496.2', "0.21924143"],
        #             ]
        #         }
        #     }
        #
        #     {
        #         "event": "authenticate",
        #         "authenticated": True
        #     }
        #
        error = self.safe_string(message, 'error')
        if error is not None:
            self.handle_error_message(client, message)
        methods: dict = {
            'subscribed': self.handle_subscription_status,
            'book': self.handle_order_book,
            'getBook': self.handle_order_book_snapshot,
            'trade': self.handle_trade,
            'candle': self.handle_ohlcv,
            'ticker24h': self.handle_ticker,
            'authenticate': self.handle_authentication_message,
            'order': self.handle_order,
            'fill': self.handle_my_trade,
            'privateCreateOrder': self.handle_single_order,
            'privateUpdateOrder': self.handle_single_order,
            'privateGetBalance': self.handle_fetch_balance,
            'privateCancelOrders': self.handle_multiple_orders,
            'privateGetOrders': self.handle_multiple_orders,
            'privateGetOrder': self.handle_single_order,
            'privateCancelOrder': self.handle_single_order,
            'privateGetOrdersOpen': self.handle_multiple_orders,
            'privateGetAccount': self.handle_trading_fees,
            'privateGetDepositHistory': self.handle_deposits,
            'privateGetWithdrawalHistory': self.handle_withdraws,
            'privateWithdrawAssets': self.handle_withdraw,
            'privateGetTrades': self.handle_my_trades,
            'getAssets': self.handle_fetch_currencies,
            'getCandles': self.handle_fetch_ohlcv,
            'getMarkets': self.handle_markets,
        }
        event = self.safe_string(message, 'event')
        method = self.safe_value(methods, event)
        if method is None:
            action = self.safe_string(message, 'action')
            method = self.safe_value(methods, action)
            if method is not None:
                method(client, message)
        else:
            method(client, message)
