"""
Health check endpoints for monitoring system status.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from ...core.database import db_manager
from ...core.config import settings
from ..dependencies import get_db_session, get_scraper_registry

logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "Alpha Grid API",
        "version": settings.app_version,
        "timestamp": "2025-07-08T03:35:00Z"
    }


@router.get("/detailed")
async def detailed_health_check(
    db: AsyncSession = Depends(get_db_session),
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Detailed health check with component status."""
    
    health_status = {
        "status": "healthy",
        "service": "Alpha Grid API",
        "version": settings.app_version,
        "components": {}
    }
    
    # Check database health
    try:
        db_healthy = await db_manager.health_check()
        health_status["components"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "type": "SQLite/PostgreSQL",
            "connection": db_healthy
        }
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    # Check scraper registry health
    try:
        registry_status = await registry.get_registry_status()
        scraper_health = await registry.health_check_all()
        
        healthy_scrapers = sum(1 for status in scraper_health.values() if status)
        total_scrapers = len(scraper_health)
        
        health_status["components"]["scrapers"] = {
            "status": "healthy" if healthy_scrapers == total_scrapers else "degraded",
            "healthy_count": healthy_scrapers,
            "total_count": total_scrapers,
            "registry_status": registry_status
        }
        
        if healthy_scrapers < total_scrapers:
            health_status["status"] = "degraded"
            
    except Exception as e:
        health_status["components"]["scrapers"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    # Check configuration
    try:
        from ...core.config import config_manager
        config_validation = config_manager.validate_configuration()
        
        health_status["components"]["configuration"] = {
            "status": "healthy" if config_validation["valid"] else "degraded",
            "validation": config_validation
        }
        
        if not config_validation["valid"]:
            health_status["status"] = "degraded"
            
    except Exception as e:
        health_status["components"]["configuration"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    logger.info(
        "Health check completed",
        overall_status=health_status["status"],
        components_checked=len(health_status["components"])
    )
    
    return health_status


@router.get("/metrics")
async def system_metrics(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get system performance metrics."""
    
    try:
        # Get scraper metrics
        scraper_metrics = await registry.get_all_metrics()
        
        # Get circuit breaker stats
        from ...core.circuit_breaker import get_all_circuit_breaker_stats
        circuit_breaker_stats = get_all_circuit_breaker_stats()
        
        metrics = {
            "scrapers": scraper_metrics,
            "circuit_breakers": circuit_breaker_stats,
            "system": {
                "uptime": "unknown",  # Would need to track startup time
                "memory_usage": "unknown",  # Would need psutil
                "cpu_usage": "unknown"
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error("Failed to get system metrics", error=str(e))
        return {
            "error": "Failed to retrieve metrics",
            "message": str(e)
        }