"""
Scraper management endpoints.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
import structlog

from ..dependencies import get_scraper_registry, require_authentication

logger = structlog.get_logger(__name__)

router = APIRouter()


class ScraperStartRequest(BaseModel):
    """Request model for starting scrapers."""
    scraper_names: Optional[List[str]] = None
    start_all: bool = False


class ScraperStopRequest(BaseModel):
    """Request model for stopping scrapers."""
    scraper_names: Optional[List[str]] = None
    stop_all: bool = False


@router.get("/")
async def list_scrapers(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """List all registered scrapers and their status."""
    
    try:
        scrapers = registry.list_scrapers()
        registry_status = await registry.get_registry_status()
        
        return {
            "scrapers": scrapers,
            "registry_status": registry_status,
            "total_scrapers": len(scrapers)
        }
        
    except Exception as e:
        logger.error("Failed to list scrapers", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list scrapers: {str(e)}"
        )


@router.get("/{scraper_name}")
async def get_scraper_details(
    scraper_name: str,
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get detailed information about a specific scraper."""
    
    try:
        scrapers = registry.list_scrapers()
        
        if scraper_name not in scrapers:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Scraper '{scraper_name}' not found"
            )
        
        scraper_info = scrapers[scraper_name]
        
        # Get metrics if scraper is running
        metrics = None
        if scraper_info["is_running"]:
            try:
                all_metrics = await registry.get_all_metrics()
                metrics = all_metrics.get(scraper_name)
            except Exception as e:
                logger.warning("Failed to get scraper metrics", scraper=scraper_name, error=str(e))
        
        return {
            "scraper": scraper_info,
            "metrics": metrics
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get scraper details", scraper=scraper_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scraper details: {str(e)}"
        )


@router.post("/start")
async def start_scrapers(
    request: ScraperStartRequest,
    registry = Depends(get_scraper_registry),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Start one or more scrapers."""
    
    try:
        if request.start_all:
            logger.info("Starting all scrapers", user=user)
            results = await registry.start_all_scrapers()
        elif request.scraper_names:
            logger.info("Starting specific scrapers", scrapers=request.scraper_names, user=user)
            results = {}
            for scraper_name in request.scraper_names:
                results[scraper_name] = await registry.start_scraper(scraper_name)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must specify either 'start_all' or 'scraper_names'"
            )
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        return {
            "message": f"Started {successful}/{total} scrapers",
            "results": results,
            "successful_count": successful,
            "total_count": total
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start scrapers", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start scrapers: {str(e)}"
        )


@router.post("/stop")
async def stop_scrapers(
    request: ScraperStopRequest,
    registry = Depends(get_scraper_registry),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Stop one or more scrapers."""
    
    try:
        if request.stop_all:
            logger.info("Stopping all scrapers", user=user)
            results = await registry.stop_all_scrapers()
        elif request.scraper_names:
            logger.info("Stopping specific scrapers", scrapers=request.scraper_names, user=user)
            results = {}
            for scraper_name in request.scraper_names:
                results[scraper_name] = await registry.stop_scraper(scraper_name)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must specify either 'stop_all' or 'scraper_names'"
            )
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        return {
            "message": f"Stopped {successful}/{total} scrapers",
            "results": results,
            "successful_count": successful,
            "total_count": total
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stop scrapers", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop scrapers: {str(e)}"
        )


@router.get("/health/check")
async def check_scraper_health(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Check health of all running scrapers."""
    
    try:
        health_results = await registry.health_check_all()
        
        healthy_count = sum(1 for is_healthy in health_results.values() if is_healthy)
        total_count = len(health_results)
        
        return {
            "overall_health": "healthy" if healthy_count == total_count else "degraded",
            "healthy_count": healthy_count,
            "total_count": total_count,
            "scraper_health": health_results
        }
        
    except Exception as e:
        logger.error("Failed to check scraper health", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check scraper health: {str(e)}"
        )


@router.get("/metrics")
async def get_scraper_metrics(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get performance metrics for all scrapers."""
    
    try:
        metrics = await registry.get_all_metrics()
        
        # Calculate aggregate metrics
        total_requests = sum(
            m.get("request_count", 0) for m in metrics.values() 
            if isinstance(m, dict) and "request_count" in m
        )
        
        total_errors = sum(
            m.get("error_count", 0) for m in metrics.values()
            if isinstance(m, dict) and "error_count" in m
        )
        
        avg_success_rate = sum(
            m.get("success_rate", 0) for m in metrics.values()
            if isinstance(m, dict) and "success_rate" in m
        ) / max(len([m for m in metrics.values() if isinstance(m, dict)]), 1)
        
        return {
            "scraper_metrics": metrics,
            "aggregate_metrics": {
                "total_requests": total_requests,
                "total_errors": total_errors,
                "average_success_rate": avg_success_rate,
                "active_scrapers": len(metrics)
            }
        }
        
    except Exception as e:
        logger.error("Failed to get scraper metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scraper metrics: {str(e)}"
        )