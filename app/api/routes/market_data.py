"""
Market data endpoints for cryptocurrency information.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
import structlog

from ..dependencies import get_scraper_registry
from ...agents.scrapers.coingecko_scraper import CoinGeckoScraper
from ...agents.scrapers.etherscan_scraper import EtherscanScraper

logger = structlog.get_logger(__name__)

router = APIRouter()


class MarketDataRequest(BaseModel):
    """Request model for market data."""
    symbols: Optional[List[str]] = Field(None, description="Cryptocurrency symbols")
    vs_currency: str = Field(default="usd", description="Target currency")
    limit: int = Field(default=20, ge=1, le=250, description="Number of results")


@router.get("/")
async def get_market_overview(
    limit: int = Query(default=20, ge=1, le=100, description="Number of top coins"),
    vs_currency: str = Query(default="usd", description="Target currency"),
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get market overview with top cryptocurrencies."""
    
    try:
        # Get CoinGecko scraper
        coingecko_scraper = registry.get_scraper("CoinGecko")
        
        if not coingecko_scraper:
            # Create temporary scraper if not running
            coingecko_scraper = CoinGeckoScraper()
        
        # Get top coins data
        top_coins = await coingecko_scraper.get_top_coins(limit=limit)
        
        # Get global market data
        global_data = await coingecko_scraper.get_global_data()
        
        # Get trending coins
        trending = await coingecko_scraper.get_trending_coins()
        
        return {
            "market_overview": {
                "top_coins": top_coins,
                "global_data": global_data,
                "trending_coins": trending[:10],  # Limit trending to top 10
                "metadata": {
                    "vs_currency": vs_currency,
                    "limit": limit,
                    "data_source": "coingecko",
                    "last_updated": top_coins[0].get("fetched_at") if top_coins else None
                }
            }
        }
        
    except Exception as e:
        logger.error("Failed to get market overview", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get market overview: {str(e)}"
        )


@router.get("/coins/{coin_id}")
async def get_coin_details(
    coin_id: str,
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get detailed information for a specific cryptocurrency."""
    
    try:
        # Get CoinGecko scraper
        coingecko_scraper = registry.get_scraper("CoinGecko")
        
        if not coingecko_scraper:
            coingecko_scraper = CoinGeckoScraper()
        
        # Get coin details
        coin_data = await coingecko_scraper.get_coin_by_id(coin_id)
        
        return {
            "coin": coin_data,
            "metadata": {
                "coin_id": coin_id,
                "data_source": "coingecko"
            }
        }
        
    except Exception as e:
        logger.error("Failed to get coin details", coin_id=coin_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get coin details: {str(e)}"
        )


@router.get("/search")
async def search_coins(
    query: str = Query(..., min_length=1, description="Search query"),
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Search for cryptocurrencies by name or symbol."""
    
    try:
        # Get CoinGecko scraper
        coingecko_scraper = registry.get_scraper("CoinGecko")
        
        if not coingecko_scraper:
            coingecko_scraper = CoinGeckoScraper()
        
        # Search coins
        search_results = await coingecko_scraper.search_coins(query)
        
        return {
            "search_results": search_results,
            "metadata": {
                "query": query,
                "results_count": len(search_results),
                "data_source": "coingecko"
            }
        }
        
    except Exception as e:
        logger.error("Failed to search coins", query=query, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search coins: {str(e)}"
        )


@router.get("/trending")
async def get_trending_coins(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get trending cryptocurrencies."""
    
    try:
        # Get CoinGecko scraper
        coingecko_scraper = registry.get_scraper("CoinGecko")
        
        if not coingecko_scraper:
            coingecko_scraper = CoinGeckoScraper()
        
        # Get trending coins
        trending_coins = await coingecko_scraper.get_trending_coins()
        
        return {
            "trending_coins": trending_coins,
            "metadata": {
                "count": len(trending_coins),
                "data_source": "coingecko"
            }
        }
        
    except Exception as e:
        logger.error("Failed to get trending coins", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get trending coins: {str(e)}"
        )


@router.get("/ethereum/address/{address}")
async def get_ethereum_address_info(
    address: str,
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get Ethereum address information."""
    
    try:
        # Validate Ethereum address format (basic check)
        if not address.startswith("0x") or len(address) != 42:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid Ethereum address format"
            )
        
        # Get Etherscan scraper
        etherscan_scraper = registry.get_scraper("Etherscan")
        
        if not etherscan_scraper:
            etherscan_scraper = EtherscanScraper()
        
        # Get address balance
        balance_info = await etherscan_scraper.get_account_balance(address)
        
        # Get recent transactions (limited)
        transactions = await etherscan_scraper.get_transaction_history(
            address, 
            offset=10  # Limit to 10 recent transactions
        )
        
        return {
            "address_info": {
                "address": address,
                "balance": balance_info,
                "recent_transactions": transactions,
                "transaction_count": len(transactions)
            },
            "metadata": {
                "data_source": "etherscan",
                "network": "ethereum"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get Ethereum address info", address=address, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Ethereum address info: {str(e)}"
        )


@router.get("/ethereum/gas")
async def get_gas_prices(
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get current Ethereum gas prices."""
    
    try:
        # Get Etherscan scraper
        etherscan_scraper = registry.get_scraper("Etherscan")
        
        if not etherscan_scraper:
            etherscan_scraper = EtherscanScraper()
        
        # Get gas prices
        gas_prices = await etherscan_scraper.get_gas_price()
        
        return {
            "gas_prices": gas_prices,
            "metadata": {
                "data_source": "etherscan",
                "network": "ethereum"
            }
        }
        
    except Exception as e:
        logger.error("Failed to get gas prices", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get gas prices: {str(e)}"
        )


@router.post("/bulk")
async def get_bulk_market_data(
    request: MarketDataRequest,
    registry = Depends(get_scraper_registry)
) -> Dict[str, Any]:
    """Get market data for multiple cryptocurrencies."""
    
    try:
        if not request.symbols:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Symbols list cannot be empty"
            )
        
        # Get CoinGecko scraper
        coingecko_scraper = registry.get_scraper("CoinGecko")
        
        if not coingecko_scraper:
            coingecko_scraper = CoinGeckoScraper()
        
        # Get market data for all symbols
        all_coins = []
        
        for symbol in request.symbols:
            try:
                # Search for coin by symbol
                search_results = await coingecko_scraper.search_coins(symbol)
                
                if search_results:
                    # Get detailed data for first match
                    coin_id = search_results[0]["id"]
                    coin_data = await coingecko_scraper.get_coin_by_id(coin_id)
                    all_coins.append(coin_data)
                else:
                    logger.warning("Coin not found", symbol=symbol)
                    
            except Exception as e:
                logger.warning("Failed to get data for symbol", symbol=symbol, error=str(e))
        
        return {
            "bulk_data": {
                "coins": all_coins,
                "requested_symbols": request.symbols,
                "found_count": len(all_coins),
                "vs_currency": request.vs_currency
            },
            "metadata": {
                "data_source": "coingecko",
                "request_time": "2025-07-08T03:35:00Z"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get bulk market data", symbols=request.symbols, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get bulk market data: {str(e)}"
        )