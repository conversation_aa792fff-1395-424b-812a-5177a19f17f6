"""
Quantum computing endpoints for cryptocurrency analysis.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from pydantic import BaseModel, Field
import structlog
import polars as pl
import io

from ..dependencies import get_quantum_calculator, require_authentication
from ...agents.quantum import QuantumScoreCalculator

logger = structlog.get_logger(__name__)

router = APIRouter()


class QuantumScoreRequest(BaseModel):
    """Request model for quantum score calculation."""
    symbols: List[str] = Field(..., description="List of cryptocurrency symbols")
    market_data: Optional[List[Dict[str, Any]]] = Field(None, description="Optional market data")


class QuantumTrainingRequest(BaseModel):
    """Request model for quantum model training."""
    max_iterations: int = Field(default=50, ge=10, le=200, description="Maximum training iterations")
    target_column: str = Field(default="price_change_percentage_24h", description="Target variable")


@router.get("/")
async def quantum_info(
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Get quantum system information and capabilities."""
    
    try:
        model_info = calculator.get_model_info()
        
        return {
            "quantum_system": {
                "status": "operational",
                "backend": "AerSimulator",
                "qiskit_version": "2.1.0"
            },
            "model_info": model_info,
            "capabilities": {
                "encoding_types": ["amplitude", "angle", "basis"],
                "max_qubits": 10,
                "supported_optimizers": ["BFGS", "COBYLA"],
                "features": [
                    "Quantum feature encoding",
                    "Variational quantum algorithms",
                    "Circuit optimization",
                    "Model persistence"
                ]
            }
        }
        
    except Exception as e:
        logger.error("Failed to get quantum info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get quantum information: {str(e)}"
        )


@router.post("/score")
async def calculate_quantum_scores(
    request: QuantumScoreRequest,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Calculate quantum scores for cryptocurrency symbols."""
    
    try:
        if request.market_data:
            # Use provided market data
            df = pl.DataFrame(request.market_data)
        else:
            # Create sample data for demonstration
            import numpy as np
            sample_data = []
            for symbol in request.symbols:
                sample_data.append({
                    "symbol": symbol,
                    "price_change_percentage_24h": np.random.uniform(-10, 10),
                    "volume_to_market_cap_ratio": np.random.uniform(0.01, 0.2),
                    "market_cap_rank": np.random.randint(1, 1000),
                    "circulating_supply": np.random.uniform(1e6, 1e12)
                })
            df = pl.DataFrame(sample_data)
        
        # Calculate quantum scores
        results = calculator.calculate_quantum_score(df)
        
        logger.info(
            "Quantum scores calculated",
            symbols=request.symbols,
            average_score=results.get("average_score"),
            model_trained=results.get("model_trained")
        )
        
        return {
            "request": {
                "symbols": request.symbols,
                "data_provided": request.market_data is not None
            },
            "results": results,
            "metadata": {
                "calculation_time": results.get("calculation_time"),
                "model_status": "trained" if results.get("model_trained") else "untrained",
                "quantum_backend": "AerSimulator"
            }
        }
        
    except Exception as e:
        logger.error("Failed to calculate quantum scores", error=str(e), symbols=request.symbols)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate quantum scores: {str(e)}"
        )


@router.post("/train")
async def train_quantum_model(
    request: QuantumTrainingRequest,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Train the quantum model with sample data."""
    
    try:
        # Create sample training data
        import numpy as np
        training_data = []
        
        for i in range(100):  # Generate 100 sample data points
            training_data.append({
                "symbol": f"COIN{i}",
                "price_change_percentage_24h": np.random.uniform(-20, 20),
                "volume_to_market_cap_ratio": np.random.uniform(0.001, 0.5),
                "market_cap_rank": np.random.randint(1, 2000),
                "circulating_supply": np.random.uniform(1e6, 1e12)
            })
        
        df = pl.DataFrame(training_data)
        
        logger.info(
            "Starting quantum model training",
            max_iterations=request.max_iterations,
            target_column=request.target_column,
            user=user
        )
        
        # Train the model
        training_results = calculator.train_quantum_model(
            df,
            max_iterations=request.max_iterations,
            target_column=request.target_column
        )
        
        logger.info(
            "Quantum model training completed",
            final_cost=training_results.get("final_cost"),
            iterations=training_results.get("iterations"),
            user=user
        )
        
        return {
            "message": "Quantum model training completed",
            "training_results": training_results,
            "model_info": calculator.get_model_info()
        }
        
    except Exception as e:
        logger.error("Failed to train quantum model", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to train quantum model: {str(e)}"
        )


@router.post("/upload-data")
async def upload_training_data(
    file: UploadFile = File(...),
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Upload CSV data for quantum model training."""
    
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV files are supported"
            )
        
        # Read file content
        content = await file.read()
        
        # Parse CSV with Polars
        df = pl.read_csv(io.BytesIO(content))
        
        logger.info(
            "Training data uploaded",
            filename=file.filename,
            rows=df.height,
            columns=df.width,
            user=user
        )
        
        # Validate required columns
        required_columns = ["symbol", "price_change_percentage_24h"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required columns: {missing_columns}"
            )
        
        return {
            "message": "Training data uploaded successfully",
            "file_info": {
                "filename": file.filename,
                "size_bytes": len(content),
                "rows": df.height,
                "columns": df.width,
                "column_names": df.columns
            },
            "data_preview": df.head(5).to_dicts(),
            "ready_for_training": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload training data", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload training data: {str(e)}"
        )


@router.get("/model/info")
async def get_model_info(
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Get detailed quantum model information."""
    
    try:
        model_info = calculator.get_model_info()
        
        return {
            "model_info": model_info,
            "quantum_features": {
                "feature_encoder": calculator.feature_encoder.get_encoding_stats(),
                "circuit_optimizer": calculator.circuit_optimizer.get_optimization_statistics()
            }
        }
        
    except Exception as e:
        logger.error("Failed to get model info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get model information: {str(e)}"
        )


@router.post("/model/save")
async def save_quantum_model(
    filename: Optional[str] = None,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Save the trained quantum model."""
    
    try:
        if not calculator.is_trained:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No trained model to save"
            )
        
        filepath = calculator.save_model(filename)
        
        logger.info("Quantum model saved", filepath=filepath, user=user)
        
        return {
            "message": "Quantum model saved successfully",
            "filepath": filepath,
            "model_info": calculator.get_model_info()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to save quantum model", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save quantum model: {str(e)}"
        )