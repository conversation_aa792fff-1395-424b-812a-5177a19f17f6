"""
Enhanced quantum computing endpoints for cryptocurrency analysis.
Includes multi-source data fetching, advanced training, and uncertainty quantification.
"""

from typing import Dict, Any, List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query, BackgroundTasks
from pydantic import BaseModel, Field, validator
import structlog
import polars as pl
import io
import asyncio
from datetime import datetime

from ..dependencies import get_quantum_calculator, require_authentication
from ...agents.quantum import QuantumScoreCalculator
from ...agents.quantum.enhanced_score_generator import Enhanced<PERSON>uantumScoreGenerator, RiskLevel, InvestmentRecommendation
from ...agents.quantum.enhanced_training_pipeline import EnhancedQuantumTrainer, TrainingConfig, OptimizerType
from ...agents.quantum.data_validator import QuantumDataValidator, ValidationConfig, ValidationLevel
from ...agents.scrapers.multi_source_fetcher import MultiSourceDataFetcher, DataSource, DataFetchConfig

logger = structlog.get_logger(__name__)

router = APIRouter()


class EnhancedQuantumScoreRequest(BaseModel):
    """Enhanced request model for quantum score calculation."""
    symbols: Optional[List[str]] = Field(None, description="Specific cryptocurrency symbols (optional)")
    limit: int = Field(default=50, ge=1, le=100, description="Number of cryptocurrencies to analyze")
    data_sources: List[str] = Field(default=["coingecko"], description="Data sources to use")
    include_portfolio_analysis: bool = Field(default=True, description="Include portfolio-level analysis")
    confidence_level: float = Field(default=0.95, ge=0.5, le=0.99, description="Confidence level for intervals")
    enable_uncertainty: bool = Field(default=True, description="Enable uncertainty quantification")

    @validator('data_sources')
    def validate_data_sources(cls, v):
        valid_sources = ["coingecko", "ccxt", "yfinance"]
        for source in v:
            if source not in valid_sources:
                raise ValueError(f"Invalid data source: {source}. Valid sources: {valid_sources}")
        return v


class EnhancedTrainingRequest(BaseModel):
    """Enhanced request model for quantum model training."""
    max_iterations: int = Field(default=100, ge=10, le=500, description="Maximum training iterations")
    optimizer_type: str = Field(default="spsa", description="Optimizer type (spsa, cobyla, l_bfgs_b)")
    learning_rate: float = Field(default=0.1, ge=0.001, le=1.0, description="Learning rate")
    num_layers: int = Field(default=2, ge=1, le=5, description="Number of quantum circuit layers")
    num_qubits: int = Field(default=4, ge=2, le=8, description="Number of qubits")
    early_stopping_patience: int = Field(default=10, ge=5, le=50, description="Early stopping patience")
    validation_split: float = Field(default=0.2, ge=0.1, le=0.4, description="Validation data split ratio")
    enable_hyperparameter_optimization: bool = Field(default=False, description="Enable hyperparameter optimization")
    data_sources: List[str] = Field(default=["coingecko"], description="Data sources for training")
    symbols_limit: int = Field(default=100, ge=50, le=500, description="Number of symbols for training")

    @validator('optimizer_type')
    def validate_optimizer(cls, v):
        valid_optimizers = ["spsa", "cobyla", "l_bfgs_b"]
        if v not in valid_optimizers:
            raise ValueError(f"Invalid optimizer: {v}. Valid optimizers: {valid_optimizers}")
        return v


class DataValidationRequest(BaseModel):
    """Request model for data validation."""
    validation_level: str = Field(default="moderate", description="Validation strictness level")
    symbols: Optional[List[str]] = Field(None, description="Specific symbols to validate")
    data_sources: List[str] = Field(default=["coingecko"], description="Data sources to validate")

    @validator('validation_level')
    def validate_level(cls, v):
        valid_levels = ["strict", "moderate", "lenient"]
        if v not in valid_levels:
            raise ValueError(f"Invalid validation level: {v}. Valid levels: {valid_levels}")
        return v


class QuantumHealthRequest(BaseModel):
    """Request model for quantum system health check."""
    include_performance_metrics: bool = Field(default=True, description="Include performance metrics")
    test_quantum_circuits: bool = Field(default=False, description="Test quantum circuit execution")


# Legacy request models for backward compatibility
class QuantumScoreRequest(BaseModel):
    """Legacy request model for quantum score calculation."""
    symbols: List[str] = Field(..., description="List of cryptocurrency symbols")
    market_data: Optional[List[Dict[str, Any]]] = Field(None, description="Optional market data")


class QuantumTrainingRequest(BaseModel):
    """Legacy request model for quantum model training."""
    max_iterations: int = Field(default=50, ge=10, le=200, description="Maximum training iterations")
    target_column: str = Field(default="price_change_percentage_24h", description="Target variable")


@router.get("/")
async def quantum_system_info(
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Get comprehensive quantum system information and capabilities."""

    try:
        model_info = calculator.get_model_info()

        # Check system health
        health_status = calculator.check_quantum_system_health()

        return {
            "quantum_system": {
                "status": health_status.get("overall_status", "unknown"),
                "backend": "AerSimulator",
                "qiskit_version": "2.1.0",
                "enhanced_features_enabled": True
            },
            "model_info": model_info,
            "capabilities": {
                "encoding_types": ["amplitude", "angle", "basis"],
                "max_qubits": 8,
                "supported_optimizers": ["SPSA", "COBYLA", "L-BFGS-B"],
                "data_sources": ["CoinGecko", "CCXT", "YFinance"],
                "features": [
                    "Multi-source data fetching",
                    "Enhanced quantum training with validation",
                    "Uncertainty quantification",
                    "Portfolio analysis",
                    "Risk assessment",
                    "Investment recommendations",
                    "Quantum advantage measurement",
                    "Classical fallback safety",
                    "Hyperparameter optimization",
                    "Model persistence and versioning"
                ]
            },
            "health_status": health_status,
            "api_version": "2.0"
        }

    except Exception as e:
        logger.error("Failed to get quantum system info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get quantum system information: {str(e)}"
        )


@router.post("/analyze")
async def enhanced_quantum_analysis(
    request: EnhancedQuantumScoreRequest,
    background_tasks: BackgroundTasks,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """
    Perform enhanced quantum cryptocurrency analysis with uncertainty quantification.
    """

    try:
        start_time = datetime.now()

        logger.info(
            "Starting enhanced quantum analysis",
            symbols=request.symbols,
            limit=request.limit,
            data_sources=request.data_sources,
            confidence_level=request.confidence_level
        )

        # Initialize components
        data_fetcher = MultiSourceDataFetcher()
        score_generator = EnhancedQuantumScoreGenerator(
            quantum_calculator=calculator,
            enable_uncertainty_quantification=request.enable_uncertainty
        )

        # Convert data sources
        sources = []
        for source_str in request.data_sources:
            if source_str == "coingecko":
                sources.append(DataSource.COINGECKO)
            elif source_str == "ccxt":
                sources.append(DataSource.CCXT)
            elif source_str == "yfinance":
                sources.append(DataSource.YFINANCE)

        # Fetch market data
        if request.symbols:
            market_data, fetch_metadata = await data_fetcher.get_specific_cryptocurrencies(
                symbols=request.symbols,
                sources=sources
            )
        else:
            market_data, fetch_metadata = await data_fetcher.get_top_cryptocurrencies(
                limit=request.limit,
                sources=sources
            )

        if market_data.height == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No market data found for the specified criteria"
            )

        # Generate quantum scores
        individual_scores, portfolio_analysis = score_generator.generate_quantum_scores(
            market_data=market_data,
            include_portfolio_analysis=request.include_portfolio_analysis,
            confidence_level=request.confidence_level
        )

        # Format results
        analysis_time = (datetime.now() - start_time).total_seconds()

        # Convert scores to serializable format
        scores_data = []
        for score in individual_scores:
            scores_data.append({
                "symbol": score.symbol,
                "quantum_score": score.score,
                "confidence_interval": {
                    "lower": score.confidence_interval[0],
                    "upper": score.confidence_interval[1],
                    "confidence_level": score.confidence_level
                },
                "uncertainty": score.uncertainty,
                "risk_level": score.risk_level.value,
                "recommendation": score.recommendation.value,
                "contributing_factors": score.contributing_factors,
                "quantum_advantage": score.quantum_advantage,
                "classical_fallback": score.classical_fallback,
                "timestamp": score.timestamp.isoformat()
            })

        # Format portfolio analysis
        portfolio_data = None
        if portfolio_analysis:
            portfolio_data = {
                "total_score": portfolio_analysis.total_score,
                "weighted_score": portfolio_analysis.weighted_score,
                "portfolio_risk": portfolio_analysis.portfolio_risk.value,
                "diversification_score": portfolio_analysis.diversification_score,
                "risk_metrics": portfolio_analysis.risk_metrics,
                "recommended_allocation": portfolio_analysis.recommended_allocation,
                "rebalancing_suggestions": portfolio_analysis.rebalancing_suggestions,
                "quantum_advantage_portfolio": portfolio_analysis.quantum_advantage_portfolio
            }

        # Close data fetcher
        background_tasks.add_task(data_fetcher.close)

        result = {
            "analysis_results": {
                "individual_scores": scores_data,
                "portfolio_analysis": portfolio_data,
                "summary": {
                    "total_assets_analyzed": len(individual_scores),
                    "average_quantum_score": sum(s.score for s in individual_scores) / len(individual_scores),
                    "high_confidence_predictions": sum(1 for s in individual_scores if s.uncertainty < 0.2),
                    "quantum_advantage_detected": any(s.quantum_advantage and s.quantum_advantage > 0.05 for s in individual_scores),
                    "classical_fallback_used": any(s.classical_fallback for s in individual_scores)
                }
            },
            "metadata": {
                "request_parameters": {
                    "symbols": request.symbols,
                    "limit": request.limit,
                    "data_sources": request.data_sources,
                    "confidence_level": request.confidence_level,
                    "uncertainty_enabled": request.enable_uncertainty
                },
                "data_fetch_info": fetch_metadata,
                "analysis_time_seconds": analysis_time,
                "timestamp": start_time.isoformat(),
                "api_version": "2.0"
            }
        }

        logger.info(
            "Enhanced quantum analysis completed",
            assets_analyzed=len(individual_scores),
            analysis_time=analysis_time,
            quantum_advantage_detected=result["analysis_results"]["summary"]["quantum_advantage_detected"]
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Enhanced quantum analysis failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Enhanced quantum analysis failed: {str(e)}"
        )


@router.post("/score")
async def calculate_quantum_scores(
    request: QuantumScoreRequest,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Calculate quantum scores for cryptocurrency symbols."""
    
    try:
        if request.market_data:
            # Use provided market data
            df = pl.DataFrame(request.market_data)
        else:
            # Create sample data for demonstration
            import numpy as np
            sample_data = []
            for symbol in request.symbols:
                sample_data.append({
                    "symbol": symbol,
                    "price_change_percentage_24h": np.random.uniform(-10, 10),
                    "volume_to_market_cap_ratio": np.random.uniform(0.01, 0.2),
                    "market_cap_rank": np.random.randint(1, 1000),
                    "circulating_supply": np.random.uniform(1e6, 1e12)
                })
            df = pl.DataFrame(sample_data)
        
        # Calculate quantum scores
        results = calculator.calculate_quantum_score(df)
        
        logger.info(
            "Quantum scores calculated",
            symbols=request.symbols,
            average_score=results.get("average_score"),
            model_trained=results.get("model_trained")
        )
        
        return {
            "request": {
                "symbols": request.symbols,
                "data_provided": request.market_data is not None
            },
            "results": results,
            "metadata": {
                "calculation_time": results.get("calculation_time"),
                "model_status": "trained" if results.get("model_trained") else "untrained",
                "quantum_backend": "AerSimulator"
            }
        }
        
    except Exception as e:
        logger.error("Failed to calculate quantum scores", error=str(e), symbols=request.symbols)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate quantum scores: {str(e)}"
        )


@router.post("/train")
async def enhanced_quantum_training(
    request: EnhancedTrainingRequest,
    background_tasks: BackgroundTasks,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """
    Enhanced quantum model training with validation, overfitting prevention, and hyperparameter optimization.
    """

    try:
        start_time = datetime.now()

        logger.info(
            "Starting enhanced quantum model training",
            max_iterations=request.max_iterations,
            optimizer_type=request.optimizer_type,
            num_qubits=request.num_qubits,
            num_layers=request.num_layers,
            hyperparameter_optimization=request.enable_hyperparameter_optimization,
            user=user
        )

        # Initialize components
        data_fetcher = MultiSourceDataFetcher()
        data_validator = QuantumDataValidator()

        # Convert data sources
        sources = []
        for source_str in request.data_sources:
            if source_str == "coingecko":
                sources.append(DataSource.COINGECKO)
            elif source_str == "ccxt":
                sources.append(DataSource.CCXT)
            elif source_str == "yfinance":
                sources.append(DataSource.YFINANCE)

        # Fetch training data
        market_data, fetch_metadata = await data_fetcher.get_top_cryptocurrencies(
            limit=request.symbols_limit,
            sources=sources
        )

        if market_data.height < 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient training data: {market_data.height} samples (minimum 50 required)"
            )

        # Validate and preprocess data
        validation_result = data_validator.validate_and_preprocess(market_data)

        if not validation_result.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Data validation failed: {validation_result.issues}"
            )

        # Create train/validation/test splits
        train_data, val_data, test_data = data_validator.create_train_validation_test_split(
            validation_result.cleaned_data,
            train_ratio=0.8 - request.validation_split,
            val_ratio=request.validation_split,
            test_ratio=0.2
        )

        # Create training configuration
        optimizer_type_enum = OptimizerType(request.optimizer_type)
        training_config = TrainingConfig(
            max_iterations=request.max_iterations,
            optimizer_type=optimizer_type_enum,
            learning_rate=request.learning_rate,
            num_layers=request.num_layers,
            num_qubits=request.num_qubits,
            early_stopping_patience=request.early_stopping_patience
        )

        # Initialize enhanced trainer
        trainer = EnhancedQuantumTrainer(training_config)

        # Perform hyperparameter optimization if requested
        hp_results = None
        if request.enable_hyperparameter_optimization:
            logger.info("Starting hyperparameter optimization")

            param_grid = {
                "learning_rate": [0.05, 0.1, 0.2],
                "num_layers": [1, 2, 3],
                "early_stopping_patience": [5, 10, 15]
            }

            hp_results = trainer.hyperparameter_optimization(
                train_data=train_data,
                val_data=val_data,
                param_grid=param_grid,
                max_trials=9
            )

            # Update config with best hyperparameters
            if hp_results["best_config"]:
                for key, value in hp_results["best_config"].items():
                    setattr(training_config, key, value)
                trainer = EnhancedQuantumTrainer(training_config)

            logger.info(
                "Hyperparameter optimization completed",
                best_config=hp_results["best_config"],
                best_val_cost=hp_results["best_val_cost"]
            )

        # Train the model
        model_name = f"quantum_crypto_model_{start_time.strftime('%Y%m%d_%H%M%S')}"
        training_result = trainer.train_model(
            train_data=train_data,
            val_data=val_data,
            test_data=test_data,
            model_name=model_name
        )

        # Update calculator with trained model if successful
        if training_result.success and training_result.final_parameters is not None:
            calculator.model_parameters = training_result.final_parameters
            calculator.is_trained = True

        # Close data fetcher
        background_tasks.add_task(data_fetcher.close)

        training_time = (datetime.now() - start_time).total_seconds()

        # Format training metrics
        metrics_data = []
        for metric in training_result.training_metrics:
            metrics_data.append({
                "iteration": metric.iteration,
                "train_cost": metric.train_cost,
                "val_cost": metric.val_cost,
                "test_cost": metric.test_cost,
                "parameter_norm": metric.parameter_norm,
                "convergence_rate": metric.convergence_rate,
                "overfitting_score": metric.overfitting_score,
                "timestamp": metric.timestamp.isoformat()
            })

        result = {
            "message": "Enhanced quantum model training completed",
            "training_result": {
                "success": training_result.success,
                "best_iteration": training_result.best_iteration,
                "best_validation_cost": training_result.best_val_cost,
                "final_train_cost": training_result.final_train_cost,
                "final_val_cost": training_result.final_val_cost,
                "final_test_cost": training_result.final_test_cost,
                "total_iterations": training_result.total_iterations,
                "convergence_achieved": training_result.convergence_achieved,
                "early_stopped": training_result.early_stopped,
                "overfitting_detected": training_result.overfitting_detected,
                "model_path": training_result.model_path
            },
            "training_metrics": metrics_data,
            "hyperparameters": training_result.hyperparameters,
            "hyperparameter_optimization": hp_results,
            "data_info": {
                "total_samples": validation_result.rows_processed,
                "train_samples": train_data.height,
                "val_samples": val_data.height,
                "test_samples": test_data.height,
                "data_retention_rate": validation_result.statistics.get("data_retention_rate", 0),
                "validation_issues": validation_result.issues,
                "validation_warnings": validation_result.warnings
            },
            "model_info": calculator.get_model_info(),
            "metadata": {
                "training_time_seconds": training_time,
                "data_fetch_info": fetch_metadata,
                "hyperparameter_optimization_enabled": request.enable_hyperparameter_optimization,
                "timestamp": start_time.isoformat(),
                "api_version": "2.0",
                "user": user
            }
        }

        logger.info(
            "Enhanced quantum model training completed",
            success=training_result.success,
            best_val_cost=training_result.best_val_cost,
            total_iterations=training_result.total_iterations,
            training_time=training_time,
            convergence_achieved=training_result.convergence_achieved,
            overfitting_detected=training_result.overfitting_detected,
            user=user
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Enhanced quantum model training failed", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Enhanced training failed: {str(e)}"
        )


@router.post("/upload-data")
async def upload_training_data(
    file: UploadFile = File(...),
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Upload CSV data for quantum model training."""
    
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV files are supported"
            )
        
        # Read file content
        content = await file.read()
        
        # Parse CSV with Polars
        df = pl.read_csv(io.BytesIO(content))
        
        logger.info(
            "Training data uploaded",
            filename=file.filename,
            rows=df.height,
            columns=df.width,
            user=user
        )
        
        # Validate required columns
        required_columns = ["symbol", "price_change_percentage_24h"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required columns: {missing_columns}"
            )
        
        return {
            "message": "Training data uploaded successfully",
            "file_info": {
                "filename": file.filename,
                "size_bytes": len(content),
                "rows": df.height,
                "columns": df.width,
                "column_names": df.columns
            },
            "data_preview": df.head(5).to_dicts(),
            "ready_for_training": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload training data", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload training data: {str(e)}"
        )


@router.get("/model/info")
async def get_model_info(
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """Get detailed quantum model information."""
    
    try:
        model_info = calculator.get_model_info()
        
        return {
            "model_info": model_info,
            "quantum_features": {
                "feature_encoder": calculator.feature_encoder.get_encoding_stats(),
                "circuit_optimizer": calculator.circuit_optimizer.get_optimization_statistics()
            }
        }
        
    except Exception as e:
        logger.error("Failed to get model info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get model information: {str(e)}"
        )


@router.post("/model/save")
async def save_quantum_model(
    filename: Optional[str] = None,
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator),
    user: str = Depends(require_authentication)
) -> Dict[str, Any]:
    """Save the trained quantum model."""
    
    try:
        if not calculator.is_trained:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No trained model to save"
            )
        
        filepath = calculator.save_model(filename)
        
        logger.info("Quantum model saved", filepath=filepath, user=user)
        
        return {
            "message": "Quantum model saved successfully",
            "filepath": filepath,
            "model_info": calculator.get_model_info()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to save quantum model", error=str(e), user=user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save quantum model: {str(e)}"
        )


@router.post("/validate-data")
async def validate_cryptocurrency_data(
    request: DataValidationRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    Validate cryptocurrency data for quantum analysis.
    """

    try:
        start_time = datetime.now()

        logger.info(
            "Starting data validation",
            validation_level=request.validation_level,
            symbols=request.symbols,
            data_sources=request.data_sources
        )

        # Initialize components
        data_fetcher = MultiSourceDataFetcher()

        # Convert validation level
        validation_level = ValidationLevel(request.validation_level)
        data_validator = QuantumDataValidator(
            ValidationConfig(level=validation_level)
        )

        # Convert data sources
        sources = []
        for source_str in request.data_sources:
            if source_str == "coingecko":
                sources.append(DataSource.COINGECKO)
            elif source_str == "ccxt":
                sources.append(DataSource.CCXT)
            elif source_str == "yfinance":
                sources.append(DataSource.YFINANCE)

        # Fetch data
        if request.symbols:
            market_data, fetch_metadata = await data_fetcher.get_specific_cryptocurrencies(
                symbols=request.symbols,
                sources=sources
            )
        else:
            market_data, fetch_metadata = await data_fetcher.get_top_cryptocurrencies(
                limit=50,
                sources=sources
            )

        # Validate data
        validation_result = data_validator.validate_and_preprocess(market_data)

        # Close data fetcher
        background_tasks.add_task(data_fetcher.close)

        validation_time = (datetime.now() - start_time).total_seconds()

        result = {
            "validation_result": {
                "is_valid": validation_result.is_valid,
                "rows_processed": validation_result.rows_processed,
                "rows_removed": validation_result.rows_removed,
                "data_retention_rate": validation_result.statistics.get("data_retention_rate", 0),
                "issues": validation_result.issues,
                "warnings": validation_result.warnings,
                "statistics": validation_result.statistics
            },
            "data_info": {
                "original_rows": market_data.height,
                "final_rows": validation_result.cleaned_data.height,
                "columns": validation_result.cleaned_data.width,
                "feature_columns": validation_result.statistics.get("feature_columns", 0)
            },
            "metadata": {
                "validation_level": request.validation_level,
                "data_fetch_info": fetch_metadata,
                "validation_time_seconds": validation_time,
                "timestamp": start_time.isoformat(),
                "api_version": "2.0"
            }
        }

        logger.info(
            "Data validation completed",
            is_valid=validation_result.is_valid,
            rows_processed=validation_result.rows_processed,
            validation_time=validation_time
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Data validation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Data validation failed: {str(e)}"
        )


@router.get("/health")
async def quantum_system_health_check(
    include_performance_metrics: bool = Query(default=True),
    test_quantum_circuits: bool = Query(default=False),
    calculator: QuantumScoreCalculator = Depends(get_quantum_calculator)
) -> Dict[str, Any]:
    """
    Comprehensive quantum system health check.
    """

    try:
        start_time = datetime.now()

        logger.info(
            "Starting quantum system health check",
            include_performance_metrics=include_performance_metrics,
            test_quantum_circuits=test_quantum_circuits
        )

        # Basic quantum system health
        health_status = calculator.check_quantum_system_health()

        # Test data sources if requested
        data_sources_health = {}
        if include_performance_metrics:
            try:
                data_fetcher = MultiSourceDataFetcher()
                data_sources_health = await data_fetcher.health_check()
                await data_fetcher.close()
            except Exception as e:
                data_sources_health = {"error": str(e)}

        # Test quantum circuits if requested
        circuit_test_results = {}
        if test_quantum_circuits:
            try:
                # Test basic quantum operations
                test_data = pl.DataFrame({
                    "symbol": ["TEST"],
                    "price_change_percentage_24h": [5.0],
                    "volume_to_market_cap_ratio": [0.1],
                    "market_cap_rank": [1],
                    "circulating_supply": [1e9]
                })

                test_result = calculator.calculate_quantum_score(test_data)
                circuit_test_results = {
                    "test_passed": True,
                    "test_score": test_result.get("quantum_scores", [0])[0],
                    "calculation_time": test_result.get("calculation_time", 0)
                }
            except Exception as e:
                circuit_test_results = {
                    "test_passed": False,
                    "error": str(e)
                }

        health_check_time = (datetime.now() - start_time).total_seconds()

        # Determine overall system status
        overall_status = "healthy"
        if health_status.get("overall_status") != "healthy":
            overall_status = "degraded"
        if data_sources_health.get("error") or not circuit_test_results.get("test_passed", True):
            overall_status = "unhealthy"

        result = {
            "system_status": {
                "overall_status": overall_status,
                "quantum_system": health_status,
                "data_sources": data_sources_health,
                "circuit_tests": circuit_test_results
            },
            "capabilities": {
                "quantum_computing": health_status.get("overall_status") == "healthy",
                "multi_source_data": not data_sources_health.get("error"),
                "uncertainty_quantification": True,
                "portfolio_analysis": True,
                "hyperparameter_optimization": True,
                "model_persistence": True
            },
            "metadata": {
                "health_check_time_seconds": health_check_time,
                "timestamp": start_time.isoformat(),
                "api_version": "2.0"
            }
        }

        logger.info(
            "Quantum system health check completed",
            overall_status=overall_status,
            health_check_time=health_check_time
        )

        return result

    except Exception as e:
        logger.error("Quantum system health check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )