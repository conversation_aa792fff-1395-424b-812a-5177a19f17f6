"""
FastAPI application factory and configuration.
"""

import async<PERSON>
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import structlog
import time

from ..core.config import settings
from ..core.logging import setup_logging
from ..core.database import init_database, db_manager
from ..agents.scrapers.registry import scraper_registry
from .routes import health, scrapers, quantum, market_data
from .middleware.rate_limiting import RateLimitMiddleware
from .middleware.authentication import AuthenticationMiddleware

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Alpha Grid API")
    
    # Initialize logging
    setup_logging()
    
    # Initialize database
    await init_database()
    
    # Start health monitoring for scrapers
    await scraper_registry.start_health_monitoring()
    
    logger.info("Alpha Grid API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Alpha Grid API")
    
    # Cleanup scrapers
    await scraper_registry.cleanup()
    
    # Close database connections
    await db_manager.close()
    
    logger.info("Alpha Grid API shutdown complete")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title="Alpha Grid API",
        description="Advanced cryptocurrency analysis platform with quantum computing",
        version=settings.app_version,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add routes
    setup_routes(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    return app


def setup_middleware(app: FastAPI) -> None:
    """Configure application middleware."""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["https://yourdomain.com"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Rate limiting middleware
    app.add_middleware(RateLimitMiddleware)
    
    # Authentication middleware
    app.add_middleware(AuthenticationMiddleware)
    
    # Request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()
        
        # Log request
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None
        )
        
        response = await call_next(request)
        
        # Log response
        process_time = time.time() - start_time
        logger.info(
            "Request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=f"{process_time:.3f}s"
        )
        
        # Add timing header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


def setup_routes(app: FastAPI) -> None:
    """Configure application routes."""
    
    # Include route modules
    app.include_router(
        health.router,
        prefix="/api/v1/health",
        tags=["Health"]
    )
    
    app.include_router(
        scrapers.router,
        prefix="/api/v1/scrapers",
        tags=["Scrapers"]
    )
    
    app.include_router(
        quantum.router,
        prefix="/api/v1/quantum",
        tags=["Quantum"]
    )
    
    app.include_router(
        market_data.router,
        prefix="/api/v1/market",
        tags=["Market Data"]
    )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "name": "Alpha Grid API",
            "version": settings.app_version,
            "description": "Advanced cryptocurrency analysis platform",
            "docs_url": "/docs" if settings.debug else None,
            "status": "operational"
        }


def setup_exception_handlers(app: FastAPI) -> None:
    """Configure global exception handlers."""
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """Handle ValueError exceptions."""
        logger.warning(
            "ValueError occurred",
            error=str(exc),
            url=str(request.url)
        )
        return JSONResponse(
            status_code=400,
            content={
                "error": "Bad Request",
                "message": str(exc),
                "type": "ValueError"
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger.error(
            "Unhandled exception occurred",
            error=str(exc),
            error_type=type(exc).__name__,
            url=str(request.url)
        )
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "type": type(exc).__name__
            }
        )

# Create the app instance
app = create_app()