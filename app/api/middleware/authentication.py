"""
Authentication middleware for API security.
"""

from typing import List
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger(__name__)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for API endpoints."""
    
    def __init__(
        self,
        app,
        public_paths: List[str] = None
    ):
        super().__init__(app)
        self.public_paths = public_paths or [
            "/",
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/api/v1/health"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Process request with authentication."""
        
        # Skip authentication for public paths
        if any(request.url.path.startswith(path) for path in self.public_paths):
            return await call_next(request)
        
        # Log authentication attempt
        auth_header = request.headers.get("Authorization")
        if auth_header:
            logger.debug(
                "Authentication header present",
                path=request.url.path,
                has_auth=True
            )
        else:
            logger.debug(
                "No authentication header",
                path=request.url.path,
                has_auth=False
            )
        
        # Process request (actual auth is handled by dependencies)
        response = await call_next(request)
        
        return response