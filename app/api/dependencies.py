"""
FastAPI dependencies for dependency injection.
"""

from typing import As<PERSON><PERSON>enerator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from ..core.database import get_db_session as _get_db_session
from ..core.config import settings
from ..agents.scrapers.registry import scraper_registry
from ..agents.quantum import QuantumScoreCalculator

logger = structlog.get_logger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session dependency."""
    async for session in _get_db_session():
        yield session


async def get_scraper_registry():
    """Get scraper registry dependency."""
    return scraper_registry


async def get_quantum_calculator() -> QuantumScoreCalculator:
    """Get quantum score calculator dependency."""
    # Create a shared instance (in production, this might be cached)
    calculator = QuantumScoreCalculator(
        num_qubits=4,
        num_layers=2,
        encoding_type="amplitude",
        optimizer_type="BFGS"
    )
    return calculator


async def verify_api_key(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """Verify API key authentication."""
    if not credentials:
        return None
    
    # In production, verify against database or external service
    # For now, just check against a simple key
    if credentials.credentials == settings.secret_key:
        return "authenticated_user"
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid API key",
        headers={"WWW-Authenticate": "Bearer"},
    )


async def require_authentication(
    user: Optional[str] = Depends(verify_api_key)
) -> str:
    """Require authentication for protected endpoints."""
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    async def check_rate_limit(self, client_id: str) -> bool:
        """Check if client is within rate limits."""
        import time
        
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        # Clean old requests
        if client_id in self.requests:
            self.requests[client_id] = [
                req_time for req_time in self.requests[client_id]
                if req_time > window_start
            ]
        else:
            self.requests[client_id] = []
        
        # Check limit
        if len(self.requests[client_id]) >= self.max_requests:
            return False
        
        # Add current request
        self.requests[client_id].append(current_time)
        return True


# Global rate limiter instance
rate_limiter = RateLimiter(max_requests=100, window_seconds=60)


async def check_rate_limit(client_ip: str = None) -> bool:
    """Check rate limit for client."""
    if not client_ip:
        return True
    
    allowed = await rate_limiter.check_rate_limit(client_ip)
    if not allowed:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )
    return True