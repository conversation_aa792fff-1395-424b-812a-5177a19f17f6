"""
Quantum circuit optimization for improved performance and reduced noise.
Uses modern Qiskit transpilation and optimization techniques.
"""

import time
from typing import Dict, List, Any, Optional, Tuple
import structlog
import numpy as np
from qiskit import QuantumCircuit, transpile
from qiskit.transpiler import PassManager
from qiskit.transpiler.passes import (
    Optimize1qGatesDecomposition,
    CommutativeCancellation,
    Depth,
    Size
)
from qiskit_aer import AerSimulator
from qiskit.quantum_info import process_fidelity, Operator

from ...core.logging import LoggerMixin

logger = structlog.get_logger(__name__)


class QuantumCircuitOptimizer(LoggerMixin):
    """
    Quantum circuit optimizer for reducing depth and gate count.
    Optimizes circuits for better performance on NISQ devices.
    """
    
    def __init__(self, target_backend: Optional[str] = None):
        """
        Initialize quantum circuit optimizer.

        Args:
            target_backend: Target backend for optimization (default: AerSimulator)
        """
        self.target_backend = target_backend or "aer_simulator"
        self.backend = self._get_backend()
        
        # Optimization statistics
        self.optimization_stats = {
            "circuits_optimized": 0,
            "total_depth_reduction": 0,
            "total_gate_reduction": 0,
            "average_optimization_time": 0.0
        }
        
        self.logger.info(
            "Quantum circuit optimizer initialized",
            target_backend=self.target_backend
        )
    
    def _get_backend(self) -> AerSimulator:
        """Get the target backend for optimization."""
        try:
            # Use AerSimulator as default
            backend = AerSimulator()

            self.logger.debug(
                "Backend loaded",
                backend_name="AerSimulator",
                backend_type=type(backend).__name__
            )

            return backend

        except Exception as e:
            self.logger.error("Failed to load backend", error=str(e))
            raise
    
    def create_optimization_pass_manager(self, optimization_level: int = 2) -> PassManager:
        """
        Create custom optimization pass manager.
        
        Args:
            optimization_level: Optimization level (0-3)
            
        Returns:
            Configured pass manager
        """
        try:
            pm = PassManager()
            
            if optimization_level >= 1:
                # Basic optimizations
                pm.append(Optimize1qGatesDecomposition())

            if optimization_level >= 2:
                # Advanced optimizations
                pm.append(CommutativeCancellation())

            if optimization_level >= 3:
                # Aggressive optimizations (may increase compilation time)
                pm.append(Optimize1qGatesDecomposition())
                pm.append(CommutativeCancellation())
            
            self.logger.debug(
                "Optimization pass manager created",
                optimization_level=optimization_level
            )
            
            return pm
            
        except Exception as e:
            self.logger.error("Failed to create pass manager", error=str(e))
            raise
    
    def optimize_circuit(
        self, 
        circuit: QuantumCircuit,
        optimization_level: int = 2,
        preserve_fidelity: bool = True
    ) -> Tuple[QuantumCircuit, Dict[str, Any]]:
        """
        Optimize a quantum circuit for the target backend.
        
        Args:
            circuit: Circuit to optimize
            optimization_level: Level of optimization (0-3)
            preserve_fidelity: Whether to check fidelity preservation
            
        Returns:
            Tuple of (optimized_circuit, optimization_metrics)
        """
        try:
            start_time = time.time()
            
            # Get original circuit metrics
            original_depth = circuit.depth()
            original_size = circuit.size()
            original_ops = dict(circuit.count_ops())
            
            self.logger.debug(
                "Starting circuit optimization",
                original_depth=original_depth,
                original_size=original_size,
                original_ops=original_ops
            )
            
            # Transpile for target backend
            optimized_circuit = transpile(
                circuit,
                backend=self.backend,
                optimization_level=optimization_level,
                seed_transpiler=42  # For reproducibility
            )
            
            # Apply custom optimization passes
            if optimization_level > 0:
                pass_manager = self.create_optimization_pass_manager(optimization_level)
                optimized_circuit = pass_manager.run(optimized_circuit)
            
            # Get optimized circuit metrics
            optimized_depth = optimized_circuit.depth()
            optimized_size = optimized_circuit.size()
            optimized_ops = dict(optimized_circuit.count_ops())
            
            optimization_time = time.time() - start_time
            
            # Calculate improvements
            depth_reduction = original_depth - optimized_depth
            size_reduction = original_size - optimized_size
            depth_improvement = (depth_reduction / max(original_depth, 1)) * 100
            size_improvement = (size_reduction / max(original_size, 1)) * 100
            
            # Check fidelity if requested
            fidelity = None
            if preserve_fidelity and original_size > 0 and optimized_size > 0:
                try:
                    # Only check fidelity for small circuits (computational cost)
                    if original_size <= 20 and optimized_size <= 20:
                        original_op = Operator(circuit)
                        optimized_op = Operator(optimized_circuit)
                        fidelity = process_fidelity(original_op, optimized_op)
                except Exception as e:
                    self.logger.warning("Could not compute fidelity", error=str(e))
                    fidelity = None
            
            # Update statistics
            self.optimization_stats["circuits_optimized"] += 1
            self.optimization_stats["total_depth_reduction"] += depth_reduction
            self.optimization_stats["total_gate_reduction"] += size_reduction
            
            # Update average optimization time
            total_time = (self.optimization_stats["average_optimization_time"] * 
                         (self.optimization_stats["circuits_optimized"] - 1) + optimization_time)
            self.optimization_stats["average_optimization_time"] = (
                total_time / self.optimization_stats["circuits_optimized"]
            )
            
            metrics = {
                "original_depth": original_depth,
                "optimized_depth": optimized_depth,
                "depth_reduction": depth_reduction,
                "depth_improvement_percent": depth_improvement,
                "original_size": original_size,
                "optimized_size": optimized_size,
                "size_reduction": size_reduction,
                "size_improvement_percent": size_improvement,
                "original_ops": original_ops,
                "optimized_ops": optimized_ops,
                "optimization_time": optimization_time,
                "optimization_level": optimization_level,
                "fidelity": fidelity,
                "backend": self.backend.name
            }
            
            self.logger.info(
                "Circuit optimization completed",
                depth_improvement=f"{depth_improvement:.1f}%",
                size_improvement=f"{size_improvement:.1f}%",
                optimization_time=f"{optimization_time:.3f}s",
                fidelity=fidelity
            )
            
            return optimized_circuit, metrics
            
        except Exception as e:
            self.logger.error("Failed to optimize circuit", error=str(e))
            raise
    
    def optimize_circuit_batch(
        self, 
        circuits: List[QuantumCircuit],
        optimization_level: int = 2
    ) -> Tuple[List[QuantumCircuit], List[Dict[str, Any]]]:
        """
        Optimize a batch of circuits.
        
        Args:
            circuits: List of circuits to optimize
            optimization_level: Level of optimization
            
        Returns:
            Tuple of (optimized_circuits, metrics_list)
        """
        try:
            optimized_circuits = []
            all_metrics = []
            
            self.logger.info(
                "Starting batch optimization",
                num_circuits=len(circuits),
                optimization_level=optimization_level
            )
            
            for i, circuit in enumerate(circuits):
                try:
                    optimized_circuit, metrics = self.optimize_circuit(
                        circuit, 
                        optimization_level=optimization_level,
                        preserve_fidelity=False  # Skip fidelity for batch processing
                    )
                    
                    optimized_circuits.append(optimized_circuit)
                    all_metrics.append(metrics)
                    
                    if (i + 1) % 10 == 0:
                        self.logger.debug(
                            "Batch optimization progress",
                            completed=i + 1,
                            total=len(circuits)
                        )
                        
                except Exception as e:
                    self.logger.warning(
                        "Failed to optimize circuit in batch",
                        circuit_index=i,
                        error=str(e)
                    )
                    # Add original circuit if optimization fails
                    optimized_circuits.append(circuit)
                    all_metrics.append({"error": str(e)})
            
            # Calculate batch statistics
            successful_optimizations = [m for m in all_metrics if "error" not in m]
            if successful_optimizations:
                avg_depth_improvement = np.mean([
                    m["depth_improvement_percent"] for m in successful_optimizations
                ])
                avg_size_improvement = np.mean([
                    m["size_improvement_percent"] for m in successful_optimizations
                ])
                
                self.logger.info(
                    "Batch optimization completed",
                    successful_optimizations=len(successful_optimizations),
                    failed_optimizations=len(all_metrics) - len(successful_optimizations),
                    avg_depth_improvement=f"{avg_depth_improvement:.1f}%",
                    avg_size_improvement=f"{avg_size_improvement:.1f}%"
                )
            
            return optimized_circuits, all_metrics
            
        except Exception as e:
            self.logger.error("Failed to optimize circuit batch", error=str(e))
            raise
    
    def analyze_circuit_complexity(self, circuit: QuantumCircuit) -> Dict[str, Any]:
        """
        Analyze circuit complexity and suggest optimizations.
        
        Args:
            circuit: Circuit to analyze
            
        Returns:
            Analysis results and recommendations
        """
        try:
            # Basic metrics
            depth = circuit.depth()
            size = circuit.size()
            num_qubits = circuit.num_qubits
            ops = dict(circuit.count_ops())
            
            # Calculate complexity scores
            depth_score = min(depth / 100, 1.0)  # Normalize to 0-1
            size_score = min(size / 1000, 1.0)   # Normalize to 0-1
            complexity_score = (depth_score + size_score) / 2
            
            # Generate recommendations
            recommendations = []
            
            if depth > 50:
                recommendations.append("Consider reducing circuit depth for better NISQ performance")
            
            if size > 500:
                recommendations.append("Large circuit size may benefit from decomposition")
            
            if "cx" in ops and ops["cx"] > num_qubits * 5:
                recommendations.append("High CNOT count - consider gate optimization")
            
            if num_qubits > 10:
                recommendations.append("Large qubit count - ensure backend compatibility")
            
            # Estimate execution characteristics
            estimated_shots_needed = max(1000, size * 10)  # Rough estimate
            estimated_noise_impact = complexity_score * 0.1  # Rough estimate
            
            analysis = {
                "depth": depth,
                "size": size,
                "num_qubits": num_qubits,
                "gate_counts": ops,
                "complexity_score": complexity_score,
                "depth_score": depth_score,
                "size_score": size_score,
                "recommendations": recommendations,
                "estimated_shots_needed": estimated_shots_needed,
                "estimated_noise_impact": estimated_noise_impact,
                "optimization_potential": {
                    "depth_reduction_potential": max(0, (depth - 10) / depth) if depth > 0 else 0,
                    "size_reduction_potential": max(0, (size - num_qubits) / size) if size > 0 else 0
                }
            }
            
            self.logger.debug(
                "Circuit complexity analyzed",
                complexity_score=complexity_score,
                num_recommendations=len(recommendations)
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error("Failed to analyze circuit complexity", error=str(e))
            raise
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """Get optimization statistics."""
        stats = self.optimization_stats.copy()
        
        # Calculate averages
        if stats["circuits_optimized"] > 0:
            stats["average_depth_reduction"] = (
                stats["total_depth_reduction"] / stats["circuits_optimized"]
            )
            stats["average_gate_reduction"] = (
                stats["total_gate_reduction"] / stats["circuits_optimized"]
            )
        else:
            stats["average_depth_reduction"] = 0
            stats["average_gate_reduction"] = 0
        
        stats["backend_info"] = {
            "name": "AerSimulator",
            "type": type(self.backend).__name__,
            "description": "Qiskit Aer quantum circuit simulator"
        }
        
        return stats
    
    def reset_statistics(self) -> None:
        """Reset optimization statistics."""
        self.optimization_stats = {
            "circuits_optimized": 0,
            "total_depth_reduction": 0,
            "total_gate_reduction": 0,
            "average_optimization_time": 0.0
        }
        self.logger.info("Optimization statistics reset")