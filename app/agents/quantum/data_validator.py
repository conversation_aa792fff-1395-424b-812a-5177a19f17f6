"""
Data validation and preprocessing pipeline for quantum cryptocurrency analysis.
Implements comprehensive data cleaning, validation, and feature engineering.
"""

import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import structlog

logger = structlog.get_logger(__name__)


class ValidationLevel(Enum):
    """Data validation strictness levels."""
    STRICT = "strict"
    MODERATE = "moderate"
    LENIENT = "lenient"


@dataclass
class ValidationConfig:
    """Configuration for data validation."""
    level: ValidationLevel = ValidationLevel.MODERATE
    min_price: float = 0.0001
    max_price: float = 1000000.0
    min_volume: float = 0.0
    max_volume_ratio: float = 10.0
    required_columns: List[str] = None
    outlier_threshold: float = 3.0
    fill_missing_values: bool = True
    remove_duplicates: bool = True
    
    def __post_init__(self):
        if self.required_columns is None:
            self.required_columns = [
                "symbol", "price", "price_change_percentage_24h", 
                "volume_24h", "volume_to_market_cap_ratio"
            ]


@dataclass
class ValidationResult:
    """Result of data validation."""
    is_valid: bool
    cleaned_data: pl.DataFrame
    issues: List[str]
    warnings: List[str]
    statistics: Dict[str, Any]
    rows_removed: int
    rows_processed: int


class QuantumDataValidator:
    """Data validator and preprocessor for quantum cryptocurrency analysis."""
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """Initialize the data validator."""
        self.config = config or ValidationConfig()
        self.logger = logger.bind(component="quantum_data_validator")
        
        # Feature engineering parameters
        self.feature_columns = [
            "price_change_percentage_24h",
            "volume_to_market_cap_ratio", 
            "market_cap_rank",
            "circulating_supply",
            "price_volatility",
            "volume_trend",
            "market_momentum"
        ]
        
        self.logger.info(
            "Quantum data validator initialized",
            validation_level=self.config.level.value,
            required_columns=len(self.config.required_columns)
        )
    
    def validate_and_preprocess(
        self, 
        data: pl.DataFrame,
        target_column: Optional[str] = None
    ) -> ValidationResult:
        """
        Validate and preprocess cryptocurrency data for quantum analysis.
        
        Args:
            data: Raw cryptocurrency data
            target_column: Target variable for supervised learning
            
        Returns:
            ValidationResult with cleaned data and validation info
        """
        start_time = datetime.now()
        original_rows = data.height
        issues = []
        warnings = []
        
        self.logger.info(
            "Starting data validation",
            rows=original_rows,
            columns=data.width,
            validation_level=self.config.level.value
        )
        
        try:
            # Step 1: Basic schema validation
            data, schema_issues = self._validate_schema(data)
            issues.extend(schema_issues)
            
            # Step 2: Remove duplicates
            if self.config.remove_duplicates:
                data, dup_count = self._remove_duplicates(data)
                if dup_count > 0:
                    warnings.append(f"Removed {dup_count} duplicate rows")
            
            # Step 3: Data type validation and conversion
            data, type_issues = self._validate_data_types(data)
            issues.extend(type_issues)
            
            # Step 4: Range validation
            data, range_issues = self._validate_ranges(data)
            issues.extend(range_issues)
            
            # Step 5: Handle missing values
            data, missing_issues = self._handle_missing_values(data)
            issues.extend(missing_issues)
            
            # Step 6: Outlier detection and handling
            data, outlier_issues = self._handle_outliers(data)
            warnings.extend(outlier_issues)
            
            # Step 7: Feature engineering
            data = self._engineer_features(data)
            
            # Step 8: Normalize features for quantum encoding
            data = self._normalize_features(data)
            
            # Step 9: Final validation
            final_issues = self._final_validation(data, target_column)
            issues.extend(final_issues)
            
            # Calculate statistics
            statistics = self._calculate_statistics(data, original_rows)
            
            # Determine if validation passed
            critical_issues = [issue for issue in issues if "critical" in issue.lower()]
            is_valid = len(critical_issues) == 0 and data.height > 0
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ValidationResult(
                is_valid=is_valid,
                cleaned_data=data,
                issues=issues,
                warnings=warnings,
                statistics=statistics,
                rows_removed=original_rows - data.height,
                rows_processed=data.height
            )
            
            self.logger.info(
                "Data validation completed",
                is_valid=is_valid,
                rows_processed=data.height,
                rows_removed=original_rows - data.height,
                processing_time=processing_time,
                issues_count=len(issues),
                warnings_count=len(warnings)
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Data validation failed", error=str(e))
            return ValidationResult(
                is_valid=False,
                cleaned_data=pl.DataFrame(),
                issues=[f"Critical validation error: {str(e)}"],
                warnings=[],
                statistics={},
                rows_removed=original_rows,
                rows_processed=0
            )
    
    def _validate_schema(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, List[str]]:
        """Validate data schema and required columns."""
        issues = []
        
        # Check for required columns
        missing_columns = [col for col in self.config.required_columns if col not in data.columns]
        if missing_columns:
            issues.append(f"Missing required columns: {missing_columns}")
            
            # Add missing columns with default values if lenient
            if self.config.level == ValidationLevel.LENIENT:
                for col in missing_columns:
                    if col == "symbol":
                        data = data.with_columns(pl.lit("UNKNOWN").alias(col))
                    elif "price" in col or "volume" in col:
                        data = data.with_columns(pl.lit(0.0).alias(col))
                    elif "rank" in col:
                        data = data.with_columns(pl.lit(999).alias(col))
                    else:
                        data = data.with_columns(pl.lit(None).alias(col))
        
        # Check for empty DataFrame
        if data.height == 0:
            issues.append("Critical: Dataset is empty")
        
        return data, issues
    
    def _remove_duplicates(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, int]:
        """Remove duplicate rows."""
        original_count = data.height
        
        # Remove duplicates based on symbol
        if "symbol" in data.columns:
            data = data.unique(subset=["symbol"], keep="first")
        else:
            data = data.unique()
        
        duplicates_removed = original_count - data.height
        return data, duplicates_removed
    
    def _validate_data_types(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, List[str]]:
        """Validate and convert data types."""
        issues = []
        
        # Define expected types
        numeric_columns = [
            "price", "price_change_24h", "price_change_percentage_24h",
            "volume_24h", "market_cap", "market_cap_rank", 
            "circulating_supply", "total_supply", "volume_to_market_cap_ratio"
        ]
        
        for col in numeric_columns:
            if col in data.columns:
                try:
                    # Convert to float, handling errors
                    data = data.with_columns(
                        pl.col(col).cast(pl.Float64, strict=False).alias(col)
                    )
                except Exception as e:
                    issues.append(f"Type conversion failed for {col}: {str(e)}")
        
        return data, issues
    
    def _validate_ranges(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, List[str]]:
        """Validate data ranges and remove invalid entries."""
        issues = []
        original_count = data.height
        
        # Price validation
        if "price" in data.columns:
            invalid_price_mask = (
                (pl.col("price") < self.config.min_price) |
                (pl.col("price") > self.config.max_price) |
                (pl.col("price").is_null()) |
                (pl.col("price").is_nan())
            )
            data = data.filter(~invalid_price_mask)
        
        # Volume validation
        if "volume_24h" in data.columns:
            invalid_volume_mask = (
                (pl.col("volume_24h") < self.config.min_volume) |
                (pl.col("volume_24h").is_null()) |
                (pl.col("volume_24h").is_nan())
            )
            data = data.filter(~invalid_volume_mask)
        
        # Volume ratio validation
        if "volume_to_market_cap_ratio" in data.columns:
            invalid_ratio_mask = (
                (pl.col("volume_to_market_cap_ratio") > self.config.max_volume_ratio) |
                (pl.col("volume_to_market_cap_ratio") < 0) |
                (pl.col("volume_to_market_cap_ratio").is_null()) |
                (pl.col("volume_to_market_cap_ratio").is_nan())
            )
            data = data.filter(~invalid_ratio_mask)
        
        rows_removed = original_count - data.height
        if rows_removed > 0:
            issues.append(f"Removed {rows_removed} rows due to invalid ranges")
        
        return data, issues
    
    def _handle_missing_values(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, List[str]]:
        """Handle missing values in the dataset."""
        issues = []
        
        if not self.config.fill_missing_values:
            # Just report missing values
            for col in data.columns:
                null_count = data.select(pl.col(col).is_null().sum()).item()
                if null_count > 0:
                    issues.append(f"Column {col} has {null_count} missing values")
            return data, issues
        
        # Fill missing values with appropriate defaults
        fill_strategies = {
            "market_cap_rank": 999,
            "circulating_supply": 0,
            "total_supply": 0,
            "volume_to_market_cap_ratio": 0.05,  # Median typical value
            "price_change_percentage_24h": 0.0,
            "price_change_24h": 0.0
        }
        
        for col, fill_value in fill_strategies.items():
            if col in data.columns:
                null_count = data.select(pl.col(col).is_null().sum()).item()
                if null_count > 0:
                    data = data.with_columns(pl.col(col).fill_null(fill_value))
                    issues.append(f"Filled {null_count} missing values in {col} with {fill_value}")
        
        return data, issues
    
    def _handle_outliers(self, data: pl.DataFrame) -> Tuple[pl.DataFrame, List[str]]:
        """Detect and handle outliers."""
        warnings = []
        
        # Columns to check for outliers
        outlier_columns = ["price_change_percentage_24h", "volume_to_market_cap_ratio"]
        
        for col in outlier_columns:
            if col in data.columns:
                # Calculate Z-score
                mean_val = data.select(pl.col(col).mean()).item()
                std_val = data.select(pl.col(col).std()).item()
                
                if std_val > 0:
                    z_scores = (data.select(pl.col(col)) - mean_val) / std_val
                    outlier_mask = abs(z_scores.to_numpy().flatten()) > self.config.outlier_threshold
                    outlier_count = sum(outlier_mask)
                    
                    if outlier_count > 0:
                        warnings.append(f"Found {outlier_count} outliers in {col}")
                        
                        # Cap outliers instead of removing them
                        if self.config.level != ValidationLevel.STRICT:
                            percentile_99 = data.select(pl.col(col).quantile(0.99)).item()
                            percentile_01 = data.select(pl.col(col).quantile(0.01)).item()
                            
                            data = data.with_columns(
                                pl.col(col).clip(percentile_01, percentile_99).alias(col)
                            )
                            warnings.append(f"Capped outliers in {col} to 1st-99th percentile range")
        
        return data, warnings
    
    def _engineer_features(self, data: pl.DataFrame) -> pl.DataFrame:
        """Engineer additional features for quantum analysis."""
        
        # Price volatility (using price change percentage as proxy)
        if "price_change_percentage_24h" in data.columns:
            data = data.with_columns(
                pl.col("price_change_percentage_24h").abs().alias("price_volatility")
            )
        
        # Volume trend (normalized volume)
        if "volume_24h" in data.columns:
            volume_median = data.select(pl.col("volume_24h").median()).item()
            data = data.with_columns(
                (pl.col("volume_24h") / volume_median).alias("volume_trend")
            )
        
        # Market momentum (combination of price change and volume)
        if "price_change_percentage_24h" in data.columns and "volume_to_market_cap_ratio" in data.columns:
            data = data.with_columns(
                (pl.col("price_change_percentage_24h") * pl.col("volume_to_market_cap_ratio")).alias("market_momentum")
            )
        
        # Market cap rank normalization
        if "market_cap_rank" in data.columns:
            max_rank = data.select(pl.col("market_cap_rank").max()).item()
            data = data.with_columns(
                (1.0 - pl.col("market_cap_rank") / max_rank).alias("market_cap_rank_normalized")
            )
        
        return data
    
    def _normalize_features(self, data: pl.DataFrame) -> pl.DataFrame:
        """Normalize features for quantum encoding."""
        
        # Features to normalize
        normalize_columns = [
            "price_change_percentage_24h", "volume_to_market_cap_ratio",
            "price_volatility", "volume_trend", "market_momentum"
        ]
        
        for col in normalize_columns:
            if col in data.columns:
                # Min-max normalization to [0, 1]
                col_min = data.select(pl.col(col).min()).item()
                col_max = data.select(pl.col(col).max()).item()
                
                if col_max > col_min:
                    data = data.with_columns(
                        ((pl.col(col) - col_min) / (col_max - col_min)).alias(f"{col}_normalized")
                    )
                else:
                    # If all values are the same, set to 0.5
                    data = data.with_columns(pl.lit(0.5).alias(f"{col}_normalized"))
        
        return data
    
    def _final_validation(self, data: pl.DataFrame, target_column: Optional[str]) -> List[str]:
        """Perform final validation checks."""
        issues = []
        
        # Check minimum data requirements
        if data.height < 10:
            issues.append("Critical: Insufficient data for quantum training (minimum 10 samples required)")
        
        # Validate target column if specified
        if target_column and target_column not in data.columns:
            issues.append(f"Critical: Target column '{target_column}' not found")
        
        # Check for required quantum features
        quantum_features = [col for col in data.columns if col.endswith("_normalized")]
        if len(quantum_features) < 3:
            issues.append("Warning: Limited features available for quantum encoding")
        
        return issues
    
    def _calculate_statistics(self, data: pl.DataFrame, original_rows: int) -> Dict[str, Any]:
        """Calculate validation statistics."""
        stats = {
            "original_rows": original_rows,
            "final_rows": data.height,
            "data_retention_rate": data.height / original_rows if original_rows > 0 else 0,
            "columns": data.width,
            "feature_columns": len([col for col in data.columns if col.endswith("_normalized")]),
            "missing_values": {}
        }
        
        # Calculate missing values per column
        for col in data.columns:
            null_count = data.select(pl.col(col).is_null().sum()).item()
            if null_count > 0:
                stats["missing_values"][col] = null_count
        
        # Calculate basic statistics for numeric columns
        numeric_stats = {}
        for col in data.columns:
            if data[col].dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]:
                try:
                    col_stats = data.select([
                        pl.col(col).mean().alias("mean"),
                        pl.col(col).std().alias("std"),
                        pl.col(col).min().alias("min"),
                        pl.col(col).max().alias("max")
                    ]).to_dicts()[0]
                    numeric_stats[col] = col_stats
                except:
                    pass
        
        stats["numeric_statistics"] = numeric_stats
        
        return stats
    
    def create_train_validation_test_split(
        self,
        data: pl.DataFrame,
        train_ratio: float = 0.8,
        val_ratio: float = 0.1,
        test_ratio: float = 0.1,
        random_seed: int = 42
    ) -> Tuple[pl.DataFrame, pl.DataFrame, pl.DataFrame]:
        """
        Create train/validation/test splits with temporal considerations.
        
        Args:
            data: Preprocessed data
            train_ratio: Training set ratio
            val_ratio: Validation set ratio  
            test_ratio: Test set ratio
            random_seed: Random seed for reproducibility
            
        Returns:
            Tuple of (train_df, val_df, test_df)
        """
        
        # Validate ratios
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("Train, validation, and test ratios must sum to 1.0")
        
        total_rows = data.height
        train_size = int(total_rows * train_ratio)
        val_size = int(total_rows * val_ratio)
        
        # For cryptocurrency data, we use random split since we don't have temporal ordering
        # In a real scenario, you might want to use temporal splits
        np.random.seed(random_seed)
        indices = np.random.permutation(total_rows)
        
        train_indices = indices[:train_size]
        val_indices = indices[train_size:train_size + val_size]
        test_indices = indices[train_size + val_size:]
        
        # Create splits
        train_df = data[train_indices]
        val_df = data[val_indices]
        test_df = data[test_indices]
        
        self.logger.info(
            "Data split created",
            train_size=len(train_indices),
            val_size=len(val_indices),
            test_size=len(test_indices),
            train_ratio=len(train_indices) / total_rows,
            val_ratio=len(val_indices) / total_rows,
            test_ratio=len(test_indices) / total_rows
        )
        
        return train_df, val_df, test_df
