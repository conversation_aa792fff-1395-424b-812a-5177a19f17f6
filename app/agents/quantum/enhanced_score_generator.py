"""
Enhanced quantum score generator with uncertainty quantification and portfolio analysis.
Provides confidence intervals, risk assessments, and investment recommendations.
"""

import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import structlog
import time
from scipy import stats
from scipy.optimize import minimize

from .quantum_score_calculator import QuantumScoreCalculator
from .data_validator import QuantumDataValidator
from .enhanced_training_pipeline import EnhancedQuantumTrainer

logger = structlog.get_logger(__name__)


class RiskLevel(Enum):
    """Investment risk levels."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


class InvestmentRecommendation(Enum):
    """Investment recommendations."""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


@dataclass
class QuantumScore:
    """Individual quantum score with uncertainty."""
    symbol: str
    score: float
    confidence_interval: Tuple[float, float]
    confidence_level: float
    uncertainty: float
    risk_level: RiskLevel
    recommendation: InvestmentRecommendation
    contributing_factors: Dict[str, float]
    quantum_advantage: Optional[float]
    classical_fallback: bool
    timestamp: datetime


@dataclass
class PortfolioAnalysis:
    """Portfolio-level quantum analysis."""
    total_score: float
    weighted_score: float
    portfolio_risk: RiskLevel
    diversification_score: float
    correlation_matrix: Optional[np.ndarray]
    risk_metrics: Dict[str, float]
    recommended_allocation: Dict[str, float]
    rebalancing_suggestions: List[str]
    quantum_advantage_portfolio: Optional[float]


@dataclass
class UncertaintyMetrics:
    """Uncertainty quantification metrics."""
    epistemic_uncertainty: float  # Model uncertainty
    aleatoric_uncertainty: float  # Data uncertainty
    total_uncertainty: float
    confidence_score: float
    prediction_interval: Tuple[float, float]
    reliability_score: float


class EnhancedQuantumScoreGenerator:
    """Enhanced quantum score generator with uncertainty quantification."""
    
    def __init__(
        self,
        quantum_calculator: Optional[QuantumScoreCalculator] = None,
        enable_uncertainty_quantification: bool = True,
        monte_carlo_samples: int = 100
    ):
        """Initialize the enhanced quantum score generator."""
        self.quantum_calculator = quantum_calculator or QuantumScoreCalculator()
        self.data_validator = QuantumDataValidator()
        self.enable_uncertainty = enable_uncertainty_quantification
        self.mc_samples = monte_carlo_samples
        
        self.logger = logger.bind(component="enhanced_quantum_score_generator")
        
        # Scoring thresholds
        self.score_thresholds = {
            InvestmentRecommendation.STRONG_BUY: 0.8,
            InvestmentRecommendation.BUY: 0.6,
            InvestmentRecommendation.HOLD: 0.4,
            InvestmentRecommendation.SELL: 0.2,
            InvestmentRecommendation.STRONG_SELL: 0.0
        }
        
        # Risk level thresholds
        self.risk_thresholds = {
            RiskLevel.VERY_LOW: 0.1,
            RiskLevel.LOW: 0.2,
            RiskLevel.MODERATE: 0.4,
            RiskLevel.HIGH: 0.6,
            RiskLevel.VERY_HIGH: 1.0
        }
        
        self.logger.info(
            "Enhanced quantum score generator initialized",
            uncertainty_enabled=self.enable_uncertainty,
            monte_carlo_samples=self.mc_samples
        )
    
    def generate_quantum_scores(
        self,
        market_data: pl.DataFrame,
        include_portfolio_analysis: bool = True,
        confidence_level: float = 0.95
    ) -> Tuple[List[QuantumScore], Optional[PortfolioAnalysis]]:
        """
        Generate quantum scores with uncertainty quantification.
        
        Args:
            market_data: Market data for cryptocurrencies
            include_portfolio_analysis: Whether to include portfolio analysis
            confidence_level: Confidence level for intervals
            
        Returns:
            Tuple of (individual scores, portfolio analysis)
        """
        start_time = time.time()
        
        self.logger.info(
            "Generating enhanced quantum scores",
            symbols=market_data.height,
            uncertainty_enabled=self.enable_uncertainty,
            confidence_level=confidence_level
        )
        
        try:
            # Validate and preprocess data
            validation_result = self.data_validator.validate_and_preprocess(market_data)
            
            if not validation_result.is_valid:
                self.logger.warning(
                    "Data validation failed, using classical fallback",
                    issues=validation_result.issues
                )
                return self._generate_classical_fallback_scores(market_data), None
            
            cleaned_data = validation_result.cleaned_data
            
            # Generate individual quantum scores
            individual_scores = []
            
            for i in range(cleaned_data.height):
                row_data = cleaned_data[i:i+1]
                score = self._generate_individual_score(
                    row_data, confidence_level
                )
                individual_scores.append(score)
            
            # Generate portfolio analysis if requested
            portfolio_analysis = None
            if include_portfolio_analysis and len(individual_scores) > 1:
                portfolio_analysis = self._generate_portfolio_analysis(
                    individual_scores, cleaned_data
                )
            
            generation_time = time.time() - start_time
            
            self.logger.info(
                "Quantum score generation completed",
                scores_generated=len(individual_scores),
                generation_time=generation_time,
                portfolio_analysis_included=portfolio_analysis is not None
            )
            
            return individual_scores, portfolio_analysis
            
        except Exception as e:
            self.logger.error("Quantum score generation failed", error=str(e))
            return self._generate_classical_fallback_scores(market_data), None
    
    def _generate_individual_score(
        self,
        row_data: pl.DataFrame,
        confidence_level: float
    ) -> QuantumScore:
        """Generate individual quantum score with uncertainty."""
        
        symbol = row_data["symbol"][0] if "symbol" in row_data.columns else "UNKNOWN"
        
        try:
            # Calculate base quantum score
            quantum_result = self.quantum_calculator.calculate_quantum_score(row_data)
            base_score = quantum_result["quantum_scores"][0]
            classical_fallback = not quantum_result.get("model_trained", False)
            
            # Calculate uncertainty if enabled
            if self.enable_uncertainty and not classical_fallback:
                uncertainty_metrics = self._calculate_uncertainty(row_data)
                confidence_interval = self._calculate_confidence_interval(
                    base_score, uncertainty_metrics, confidence_level
                )
                uncertainty = uncertainty_metrics.total_uncertainty
            else:
                uncertainty_metrics = UncertaintyMetrics(
                    epistemic_uncertainty=0.1,
                    aleatoric_uncertainty=0.1,
                    total_uncertainty=0.2,
                    confidence_score=0.5,
                    prediction_interval=(base_score - 0.1, base_score + 0.1),
                    reliability_score=0.5
                )
                confidence_interval = (base_score - 0.1, base_score + 0.1)
                uncertainty = 0.2
            
            # Determine risk level
            risk_level = self._determine_risk_level(base_score, uncertainty)
            
            # Generate investment recommendation
            recommendation = self._generate_recommendation(base_score, uncertainty)
            
            # Calculate contributing factors
            contributing_factors = self._calculate_contributing_factors(row_data)
            
            # Calculate quantum advantage
            quantum_advantage = self._calculate_quantum_advantage(row_data) if not classical_fallback else None
            
            return QuantumScore(
                symbol=symbol,
                score=base_score,
                confidence_interval=confidence_interval,
                confidence_level=confidence_level,
                uncertainty=uncertainty,
                risk_level=risk_level,
                recommendation=recommendation,
                contributing_factors=contributing_factors,
                quantum_advantage=quantum_advantage,
                classical_fallback=classical_fallback,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.warning(f"Failed to generate quantum score for {symbol}", error=str(e))
            return self._generate_fallback_score(symbol)
    
    def _calculate_uncertainty(self, row_data: pl.DataFrame) -> UncertaintyMetrics:
        """Calculate uncertainty metrics using Monte Carlo sampling."""
        
        if not self.enable_uncertainty:
            return UncertaintyMetrics(
                epistemic_uncertainty=0.1,
                aleatoric_uncertainty=0.1,
                total_uncertainty=0.2,
                confidence_score=0.5,
                prediction_interval=(0.4, 0.6),
                reliability_score=0.5
            )
        
        try:
            # Monte Carlo sampling for uncertainty estimation
            scores = []
            
            for _ in range(self.mc_samples):
                # Add noise to input features to simulate uncertainty
                noisy_data = self._add_noise_to_data(row_data)
                
                # Calculate score with noisy data
                result = self.quantum_calculator.calculate_quantum_score(noisy_data)
                scores.append(result["quantum_scores"][0])
            
            scores = np.array(scores)
            
            # Calculate uncertainty metrics
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            
            # Epistemic uncertainty (model uncertainty)
            epistemic_uncertainty = std_score * 0.7  # Approximate split
            
            # Aleatoric uncertainty (data uncertainty)
            aleatoric_uncertainty = std_score * 0.3
            
            # Total uncertainty
            total_uncertainty = std_score
            
            # Confidence score (inverse of uncertainty)
            confidence_score = max(0.0, 1.0 - total_uncertainty)
            
            # Prediction interval
            prediction_interval = (
                np.percentile(scores, 2.5),
                np.percentile(scores, 97.5)
            )
            
            # Reliability score
            reliability_score = confidence_score * (1.0 - abs(mean_score - 0.5))
            
            return UncertaintyMetrics(
                epistemic_uncertainty=epistemic_uncertainty,
                aleatoric_uncertainty=aleatoric_uncertainty,
                total_uncertainty=total_uncertainty,
                confidence_score=confidence_score,
                prediction_interval=prediction_interval,
                reliability_score=reliability_score
            )
            
        except Exception as e:
            self.logger.warning("Uncertainty calculation failed", error=str(e))
            return UncertaintyMetrics(
                epistemic_uncertainty=0.2,
                aleatoric_uncertainty=0.2,
                total_uncertainty=0.4,
                confidence_score=0.3,
                prediction_interval=(0.3, 0.7),
                reliability_score=0.3
            )
    
    def _add_noise_to_data(self, data: pl.DataFrame, noise_level: float = 0.05) -> pl.DataFrame:
        """Add noise to data for uncertainty estimation."""
        noisy_data = data.clone()
        
        # Add noise to numeric columns
        numeric_columns = [
            "price_change_percentage_24h_normalized",
            "volume_to_market_cap_ratio_normalized",
            "price_volatility_normalized",
            "volume_trend_normalized",
            "market_momentum_normalized"
        ]
        
        for col in numeric_columns:
            if col in noisy_data.columns:
                original_values = noisy_data[col].to_numpy()
                noise = np.random.normal(0, noise_level, len(original_values))
                noisy_values = np.clip(original_values + noise, 0, 1)  # Keep in [0,1] range
                noisy_data = noisy_data.with_columns(
                    pl.Series(col, noisy_values)
                )
        
        return noisy_data
    
    def _calculate_confidence_interval(
        self,
        base_score: float,
        uncertainty_metrics: UncertaintyMetrics,
        confidence_level: float
    ) -> Tuple[float, float]:
        """Calculate confidence interval for the score."""
        
        # Use normal distribution assumption
        z_score = stats.norm.ppf((1 + confidence_level) / 2)
        margin_of_error = z_score * uncertainty_metrics.total_uncertainty
        
        lower_bound = max(0.0, base_score - margin_of_error)
        upper_bound = min(1.0, base_score + margin_of_error)
        
        return (lower_bound, upper_bound)
    
    def _determine_risk_level(self, score: float, uncertainty: float) -> RiskLevel:
        """Determine risk level based on score and uncertainty."""
        
        # Risk increases with uncertainty and extreme scores
        risk_factor = uncertainty + abs(score - 0.5) * 0.5
        
        if risk_factor <= self.risk_thresholds[RiskLevel.VERY_LOW]:
            return RiskLevel.VERY_LOW
        elif risk_factor <= self.risk_thresholds[RiskLevel.LOW]:
            return RiskLevel.LOW
        elif risk_factor <= self.risk_thresholds[RiskLevel.MODERATE]:
            return RiskLevel.MODERATE
        elif risk_factor <= self.risk_thresholds[RiskLevel.HIGH]:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _generate_recommendation(self, score: float, uncertainty: float) -> InvestmentRecommendation:
        """Generate investment recommendation based on score and uncertainty."""
        
        # Adjust score based on uncertainty (more conservative with high uncertainty)
        adjusted_score = score * (1.0 - uncertainty * 0.5)
        
        if adjusted_score >= self.score_thresholds[InvestmentRecommendation.STRONG_BUY]:
            return InvestmentRecommendation.STRONG_BUY
        elif adjusted_score >= self.score_thresholds[InvestmentRecommendation.BUY]:
            return InvestmentRecommendation.BUY
        elif adjusted_score >= self.score_thresholds[InvestmentRecommendation.HOLD]:
            return InvestmentRecommendation.HOLD
        elif adjusted_score >= self.score_thresholds[InvestmentRecommendation.SELL]:
            return InvestmentRecommendation.SELL
        else:
            return InvestmentRecommendation.STRONG_SELL
    
    def _calculate_contributing_factors(self, row_data: pl.DataFrame) -> Dict[str, float]:
        """Calculate contributing factors to the quantum score."""
        
        factors = {}
        
        # Extract normalized features
        feature_columns = [
            "price_change_percentage_24h_normalized",
            "volume_to_market_cap_ratio_normalized", 
            "price_volatility_normalized",
            "volume_trend_normalized",
            "market_momentum_normalized"
        ]
        
        total_weight = 0.0
        for col in feature_columns:
            if col in row_data.columns:
                value = row_data[col][0]
                # Simple weighting scheme
                weight = value * (1.0 - abs(value - 0.5))  # Higher weight for extreme values
                factors[col.replace("_normalized", "")] = weight
                total_weight += weight
        
        # Normalize factors
        if total_weight > 0:
            factors = {k: v / total_weight for k, v in factors.items()}
        
        return factors
    
    def _calculate_quantum_advantage(self, row_data: pl.DataFrame) -> Optional[float]:
        """Calculate quantum advantage over classical methods."""
        
        try:
            # Calculate quantum score
            quantum_result = self.quantum_calculator.calculate_quantum_score(row_data)
            quantum_score = quantum_result["quantum_scores"][0]
            
            # Calculate classical score (using the fallback method)
            classical_result = self.quantum_calculator._generate_classical_fallback_scores(row_data)
            classical_score = classical_result["quantum_scores"][0]
            
            # Quantum advantage is the difference
            advantage = quantum_score - classical_score
            
            return advantage
            
        except Exception as e:
            self.logger.warning("Failed to calculate quantum advantage", error=str(e))
            return None
    
    def _generate_portfolio_analysis(
        self,
        individual_scores: List[QuantumScore],
        market_data: pl.DataFrame
    ) -> PortfolioAnalysis:
        """Generate portfolio-level analysis."""
        
        try:
            # Calculate portfolio metrics
            scores = [score.score for score in individual_scores]
            uncertainties = [score.uncertainty for score in individual_scores]
            
            # Total and weighted scores
            total_score = np.mean(scores)
            weights = self._calculate_portfolio_weights(individual_scores)
            weighted_score = np.sum([s * w for s, w in zip(scores, weights)])
            
            # Portfolio risk
            portfolio_uncertainty = np.sqrt(np.mean([u**2 for u in uncertainties]))
            portfolio_risk = self._determine_risk_level(weighted_score, portfolio_uncertainty)
            
            # Diversification score
            diversification_score = self._calculate_diversification_score(individual_scores)
            
            # Risk metrics
            risk_metrics = self._calculate_portfolio_risk_metrics(individual_scores)
            
            # Recommended allocation
            recommended_allocation = self._optimize_portfolio_allocation(individual_scores)
            
            # Rebalancing suggestions
            rebalancing_suggestions = self._generate_rebalancing_suggestions(
                individual_scores, recommended_allocation
            )
            
            # Portfolio quantum advantage
            quantum_advantages = [s.quantum_advantage for s in individual_scores if s.quantum_advantage is not None]
            portfolio_quantum_advantage = np.mean(quantum_advantages) if quantum_advantages else None
            
            return PortfolioAnalysis(
                total_score=total_score,
                weighted_score=weighted_score,
                portfolio_risk=portfolio_risk,
                diversification_score=diversification_score,
                correlation_matrix=None,  # Would need historical data
                risk_metrics=risk_metrics,
                recommended_allocation=recommended_allocation,
                rebalancing_suggestions=rebalancing_suggestions,
                quantum_advantage_portfolio=portfolio_quantum_advantage
            )
            
        except Exception as e:
            self.logger.error("Portfolio analysis failed", error=str(e))
            return PortfolioAnalysis(
                total_score=0.5,
                weighted_score=0.5,
                portfolio_risk=RiskLevel.MODERATE,
                diversification_score=0.5,
                correlation_matrix=None,
                risk_metrics={},
                recommended_allocation={},
                rebalancing_suggestions=[],
                quantum_advantage_portfolio=None
            )
    
    def _calculate_portfolio_weights(self, scores: List[QuantumScore]) -> List[float]:
        """Calculate portfolio weights based on scores and uncertainty."""
        
        # Weight by score and inverse uncertainty
        weights = []
        for score in scores:
            weight = score.score * (1.0 - score.uncertainty)
            weights.append(weight)
        
        # Normalize weights
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(scores)] * len(scores)
        
        return weights
    
    def _calculate_diversification_score(self, scores: List[QuantumScore]) -> float:
        """Calculate portfolio diversification score."""
        
        if len(scores) <= 1:
            return 0.0
        
        # Simple diversification based on score variance
        score_values = [s.score for s in scores]
        score_variance = np.var(score_values)
        
        # Higher variance indicates better diversification
        diversification_score = min(1.0, score_variance * 4)  # Scale to [0,1]
        
        return diversification_score
    
    def _calculate_portfolio_risk_metrics(self, scores: List[QuantumScore]) -> Dict[str, float]:
        """Calculate portfolio risk metrics."""
        
        score_values = [s.score for s in scores]
        uncertainties = [s.uncertainty for s in scores]
        
        return {
            "value_at_risk_95": np.percentile(score_values, 5),
            "expected_shortfall": np.mean([s for s in score_values if s <= np.percentile(score_values, 5)]),
            "volatility": np.std(score_values),
            "max_uncertainty": max(uncertainties),
            "avg_uncertainty": np.mean(uncertainties),
            "sharpe_ratio": np.mean(score_values) / (np.std(score_values) + 1e-8)
        }
    
    def _optimize_portfolio_allocation(self, scores: List[QuantumScore]) -> Dict[str, float]:
        """Optimize portfolio allocation using mean-variance optimization."""
        
        try:
            n_assets = len(scores)
            
            # Expected returns (use scores as proxy)
            expected_returns = np.array([s.score for s in scores])
            
            # Risk (use uncertainties as proxy for variance)
            risks = np.array([s.uncertainty for s in scores])
            
            # Simple covariance matrix (diagonal, assuming independence)
            cov_matrix = np.diag(risks**2)
            
            # Objective function: maximize return - risk penalty
            def objective(weights):
                portfolio_return = np.dot(weights, expected_returns)
                portfolio_risk = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
                return -(portfolio_return - 0.5 * portfolio_risk)  # Risk penalty
            
            # Constraints
            constraints = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},  # Weights sum to 1
            ]
            
            # Bounds (0 to 1 for each weight)
            bounds = [(0, 1) for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
            
            if result.success:
                optimal_weights = result.x
            else:
                optimal_weights = x0  # Fall back to equal weights
            
            # Create allocation dictionary
            allocation = {}
            for i, score in enumerate(scores):
                allocation[score.symbol] = float(optimal_weights[i])
            
            return allocation
            
        except Exception as e:
            self.logger.warning("Portfolio optimization failed", error=str(e))
            # Fall back to equal weights
            equal_weight = 1.0 / len(scores)
            return {score.symbol: equal_weight for score in scores}
    
    def _generate_rebalancing_suggestions(
        self,
        scores: List[QuantumScore],
        recommended_allocation: Dict[str, float]
    ) -> List[str]:
        """Generate rebalancing suggestions."""
        
        suggestions = []
        
        # Current equal allocation
        current_weight = 1.0 / len(scores)
        
        for score in scores:
            symbol = score.symbol
            recommended_weight = recommended_allocation.get(symbol, current_weight)
            
            weight_diff = recommended_weight - current_weight
            
            if weight_diff > 0.05:  # Increase by more than 5%
                suggestions.append(f"Increase allocation to {symbol} by {weight_diff:.1%}")
            elif weight_diff < -0.05:  # Decrease by more than 5%
                suggestions.append(f"Decrease allocation to {symbol} by {abs(weight_diff):.1%}")
        
        # Risk-based suggestions
        high_risk_assets = [s.symbol for s in scores if s.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]]
        if high_risk_assets:
            suggestions.append(f"Consider reducing exposure to high-risk assets: {', '.join(high_risk_assets)}")
        
        return suggestions
    
    def _generate_classical_fallback_scores(self, market_data: pl.DataFrame) -> List[QuantumScore]:
        """Generate classical fallback scores when quantum analysis fails."""
        
        scores = []
        
        for i in range(market_data.height):
            row = market_data[i:i+1]
            symbol = row["symbol"][0] if "symbol" in row.columns else f"ASSET_{i}"
            
            # Simple classical scoring
            price_change = row.get_column("price_change_percentage_24h")[0] if "price_change_percentage_24h" in row.columns else 0
            volume_ratio = row.get_column("volume_to_market_cap_ratio")[0] if "volume_to_market_cap_ratio" in row.columns else 0.05
            
            # Classical score based on price momentum and volume
            classical_score = max(0.0, min(1.0, (price_change + 10) / 20 * 0.7 + min(volume_ratio * 10, 1.0) * 0.3))
            
            score = QuantumScore(
                symbol=symbol,
                score=classical_score,
                confidence_interval=(classical_score - 0.1, classical_score + 0.1),
                confidence_level=0.8,
                uncertainty=0.2,
                risk_level=RiskLevel.MODERATE,
                recommendation=self._generate_recommendation(classical_score, 0.2),
                contributing_factors={"price_change": 0.7, "volume": 0.3},
                quantum_advantage=None,
                classical_fallback=True,
                timestamp=datetime.now()
            )
            scores.append(score)
        
        return scores
    
    def _generate_fallback_score(self, symbol: str) -> QuantumScore:
        """Generate a fallback score for a single asset."""
        return QuantumScore(
            symbol=symbol,
            score=0.5,
            confidence_interval=(0.3, 0.7),
            confidence_level=0.5,
            uncertainty=0.4,
            risk_level=RiskLevel.MODERATE,
            recommendation=InvestmentRecommendation.HOLD,
            contributing_factors={},
            quantum_advantage=None,
            classical_fallback=True,
            timestamp=datetime.now()
        )
