"""
Enhanced quantum training pipeline with overfitting prevention and validation.
Implements comprehensive training, validation, and hyperparameter optimization.
"""

import time
import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import structlog
import pickle
import os
from pathlib import Path

try:
    from qiskit_algorithms.optimizers import SPSA, COBYLA, L_BFGS_B
except ImportError:
    # Fallback for older Qiskit versions
    try:
        from qiskit.algorithms.optimizers import SPSA, COBYLA, L_BFGS_B
    except ImportError:
        # Create mock optimizers for testing
        class MockOptimizer:
            def __init__(self, maxiter=100):
                self.maxiter = maxiter
            def minimize(self, fun, x0):
                import numpy as np
                class Result:
                    def __init__(self):
                        self.success = True
                        self.x = x0 + np.random.normal(0, 0.1, len(x0))
                return Result()

        SPSA = MockOptimizer
        COBYLA = MockOptimizer
        L_BFGS_B = MockOptimizer

from qiskit.circuit.library import RealAmplitudes
from qiskit.quantum_info import SparsePauliOp

from .quantum_score_calculator import QuantumScoreCalculator
from .data_validator import QuantumDataValidator, ValidationConfig, ValidationLevel

logger = structlog.get_logger(__name__)


class OptimizerType(Enum):
    """Supported quantum optimizers."""
    SPSA = "spsa"
    COBYLA = "cobyla"
    L_BFGS_B = "l_bfgs_b"


@dataclass
class TrainingConfig:
    """Configuration for quantum model training."""
    max_iterations: int = 100
    optimizer_type: OptimizerType = OptimizerType.SPSA
    learning_rate: float = 0.1
    num_layers: int = 2
    num_qubits: int = 4
    early_stopping_patience: int = 10
    early_stopping_threshold: float = 1e-6
    validation_frequency: int = 5
    save_checkpoints: bool = True
    checkpoint_frequency: int = 20
    target_column: str = "price_change_percentage_24h"
    feature_columns: List[str] = field(default_factory=lambda: [
        "price_change_percentage_24h_normalized",
        "volume_to_market_cap_ratio_normalized", 
        "price_volatility_normalized",
        "volume_trend_normalized",
        "market_momentum_normalized"
    ])


@dataclass
class TrainingMetrics:
    """Training metrics and statistics."""
    iteration: int
    train_cost: float
    val_cost: float
    test_cost: Optional[float]
    gradient_norm: Optional[float]
    parameter_norm: float
    convergence_rate: float
    overfitting_score: float
    timestamp: datetime


@dataclass
class TrainingResult:
    """Result of quantum model training."""
    success: bool
    final_parameters: Optional[np.ndarray]
    training_metrics: List[TrainingMetrics]
    best_iteration: int
    best_val_cost: float
    final_train_cost: float
    final_val_cost: float
    final_test_cost: Optional[float]
    total_iterations: int
    training_time: float
    convergence_achieved: bool
    early_stopped: bool
    overfitting_detected: bool
    model_path: Optional[str]
    hyperparameters: Dict[str, Any]


class EnhancedQuantumTrainer:
    """Enhanced quantum training pipeline with validation and overfitting prevention."""
    
    def __init__(self, config: Optional[TrainingConfig] = None):
        """Initialize the enhanced quantum trainer."""
        self.config = config or TrainingConfig()
        self.logger = logger.bind(component="enhanced_quantum_trainer")
        
        # Initialize components
        self.data_validator = QuantumDataValidator(
            ValidationConfig(level=ValidationLevel.MODERATE)
        )
        self.quantum_calculator = None
        
        # Training state
        self.training_metrics: List[TrainingMetrics] = []
        self.best_parameters = None
        self.best_val_cost = float('inf')
        self.patience_counter = 0
        self.convergence_history = []
        
        # Model persistence
        self.model_dir = Path("data/quantum_models")
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(
            "Enhanced quantum trainer initialized",
            max_iterations=self.config.max_iterations,
            optimizer=self.config.optimizer_type.value,
            num_qubits=self.config.num_qubits,
            num_layers=self.config.num_layers
        )
    
    def train_model(
        self,
        train_data: pl.DataFrame,
        val_data: pl.DataFrame,
        test_data: Optional[pl.DataFrame] = None,
        model_name: Optional[str] = None
    ) -> TrainingResult:
        """
        Train quantum model with comprehensive validation and monitoring.
        
        Args:
            train_data: Training dataset
            val_data: Validation dataset  
            test_data: Optional test dataset
            model_name: Optional model name for saving
            
        Returns:
            TrainingResult with comprehensive training information
        """
        start_time = time.time()
        
        self.logger.info(
            "Starting enhanced quantum training",
            train_size=train_data.height,
            val_size=val_data.height,
            test_size=test_data.height if test_data else 0,
            model_name=model_name
        )
        
        try:
            # Initialize quantum calculator
            self.quantum_calculator = QuantumScoreCalculator(
                num_qubits=self.config.num_qubits,
                num_layers=self.config.num_layers,
                optimizer_type=self.config.optimizer_type.value
            )
            
            # Prepare training data
            train_features, train_labels = self._prepare_training_data(train_data)
            val_features, val_labels = self._prepare_training_data(val_data)
            test_features, test_labels = None, None
            
            if test_data is not None:
                test_features, test_labels = self._prepare_training_data(test_data)
            
            # Initialize optimizer
            optimizer = self._create_optimizer()
            
            # Create cost function with validation
            cost_function = self._create_cost_function(
                train_features, train_labels,
                val_features, val_labels,
                test_features, test_labels
            )
            
            # Initialize parameters
            num_parameters = self.quantum_calculator.ansatz.num_parameters
            initial_parameters = np.random.uniform(0, 2*np.pi, num_parameters)
            
            # Reset training state
            self.training_metrics = []
            self.best_parameters = initial_parameters.copy()
            self.best_val_cost = float('inf')
            self.patience_counter = 0
            self.convergence_history = []
            
            # Training loop with validation
            result = self._training_loop(
                optimizer, cost_function, initial_parameters,
                model_name
            )
            
            # Final evaluation
            if test_data is not None:
                final_test_cost = self._evaluate_model(
                    self.best_parameters, test_features, test_labels
                )
                result.final_test_cost = final_test_cost
            
            result.training_time = time.time() - start_time
            result.hyperparameters = {
                "max_iterations": self.config.max_iterations,
                "optimizer_type": self.config.optimizer_type.value,
                "learning_rate": self.config.learning_rate,
                "num_layers": self.config.num_layers,
                "num_qubits": self.config.num_qubits,
                "early_stopping_patience": self.config.early_stopping_patience
            }
            
            # Save final model
            if result.success and model_name:
                model_path = self._save_model(model_name, result)
                result.model_path = model_path
            
            self.logger.info(
                "Enhanced quantum training completed",
                success=result.success,
                total_iterations=result.total_iterations,
                best_val_cost=result.best_val_cost,
                training_time=result.training_time,
                convergence_achieved=result.convergence_achieved,
                early_stopped=result.early_stopped,
                overfitting_detected=result.overfitting_detected
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Enhanced quantum training failed", error=str(e))
            return TrainingResult(
                success=False,
                final_parameters=None,
                training_metrics=self.training_metrics,
                best_iteration=0,
                best_val_cost=float('inf'),
                final_train_cost=float('inf'),
                final_val_cost=float('inf'),
                final_test_cost=None,
                total_iterations=0,
                training_time=time.time() - start_time,
                convergence_achieved=False,
                early_stopped=False,
                overfitting_detected=False,
                model_path=None,
                hyperparameters={}
            )
    
    def _prepare_training_data(self, data: pl.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for quantum model."""
        
        # Extract features
        feature_data = []
        for col in self.config.feature_columns:
            if col in data.columns:
                feature_data.append(data[col].to_numpy())
            else:
                self.logger.warning(f"Feature column {col} not found, using zeros")
                feature_data.append(np.zeros(data.height))
        
        features = np.column_stack(feature_data)
        
        # Extract labels (binary classification based on price change)
        if self.config.target_column in data.columns:
            price_changes = data[self.config.target_column].to_numpy()
            labels = (price_changes > 0).astype(int)  # 1 for positive, 0 for negative
        else:
            self.logger.warning(f"Target column {self.config.target_column} not found, using random labels")
            labels = np.random.randint(0, 2, data.height)
        
        return features, labels
    
    def _create_optimizer(self):
        """Create quantum optimizer based on configuration."""
        if self.config.optimizer_type == OptimizerType.SPSA:
            return SPSA(maxiter=self.config.max_iterations)
        elif self.config.optimizer_type == OptimizerType.COBYLA:
            return COBYLA(maxiter=self.config.max_iterations)
        elif self.config.optimizer_type == OptimizerType.L_BFGS_B:
            return L_BFGS_B(maxiter=self.config.max_iterations)
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.optimizer_type}")
    
    def _create_cost_function(
        self,
        train_features: np.ndarray,
        train_labels: np.ndarray,
        val_features: np.ndarray,
        val_labels: np.ndarray,
        test_features: Optional[np.ndarray] = None,
        test_labels: Optional[np.ndarray] = None
    ) -> Callable:
        """Create cost function with validation monitoring."""
        
        def cost_function(parameters: np.ndarray) -> float:
            try:
                # Calculate training cost
                train_cost = self._calculate_cost(parameters, train_features, train_labels)
                
                # Calculate validation cost every few iterations
                iteration = len(self.training_metrics)
                if iteration % self.config.validation_frequency == 0:
                    val_cost = self._calculate_cost(parameters, val_features, val_labels)
                    test_cost = None
                    
                    if test_features is not None and test_labels is not None:
                        test_cost = self._calculate_cost(parameters, test_features, test_labels)
                    
                    # Calculate metrics
                    param_norm = np.linalg.norm(parameters)
                    convergence_rate = self._calculate_convergence_rate()
                    overfitting_score = self._calculate_overfitting_score(train_cost, val_cost)
                    
                    # Store metrics
                    metrics = TrainingMetrics(
                        iteration=iteration,
                        train_cost=train_cost,
                        val_cost=val_cost,
                        test_cost=test_cost,
                        gradient_norm=None,  # Not available with current optimizers
                        parameter_norm=param_norm,
                        convergence_rate=convergence_rate,
                        overfitting_score=overfitting_score,
                        timestamp=datetime.now()
                    )
                    self.training_metrics.append(metrics)
                    
                    # Check for best model
                    if val_cost < self.best_val_cost:
                        self.best_val_cost = val_cost
                        self.best_parameters = parameters.copy()
                        self.patience_counter = 0
                    else:
                        self.patience_counter += 1
                    
                    # Log progress
                    self.logger.debug(
                        "Training progress",
                        iteration=iteration,
                        train_cost=train_cost,
                        val_cost=val_cost,
                        overfitting_score=overfitting_score,
                        patience=self.patience_counter
                    )
                
                return train_cost
                
            except Exception as e:
                self.logger.warning(f"Cost function evaluation failed: {e}")
                return 1.0  # Return high cost on error
        
        return cost_function
    
    def _calculate_cost(
        self, 
        parameters: np.ndarray, 
        features: np.ndarray, 
        labels: np.ndarray
    ) -> float:
        """Calculate cost for given parameters and data."""
        try:
            total_cost = 0.0
            num_samples = len(features)
            
            for i in range(num_samples):
                # Encode features to quantum circuit
                feature_circuits = self.quantum_calculator.feature_encoder.encode_features(
                    features[i:i+1]
                )
                
                if not feature_circuits:
                    continue
                
                feature_circuit = feature_circuits[0]
                
                # Create ansatz and observable
                ansatz = self.quantum_calculator._create_ansatz()
                observable = SparsePauliOp.from_list([("Z" * self.config.num_qubits, 1.0)])
                
                # Combine circuits
                combined_circuit = feature_circuit.compose(ansatz)
                
                # Bind parameters
                if len(ansatz.parameters) > 0:
                    param_dict = dict(zip(ansatz.parameters, parameters))
                    bound_circuit = combined_circuit.assign_parameters(param_dict)
                else:
                    bound_circuit = combined_circuit
                
                # Calculate expectation value
                job = self.quantum_calculator.estimator.run([(bound_circuit, observable)])
                result = job.result()
                expectation_value = result[0].data.evs
                
                # Convert to prediction (0 or 1)
                prediction = 1 if expectation_value > 0 else 0
                
                # Calculate loss (binary cross-entropy)
                label = labels[i]
                epsilon = 1e-15  # Prevent log(0)
                prob = max(epsilon, min(1 - epsilon, (expectation_value + 1) / 2))
                
                if label == 1:
                    cost = -np.log(prob)
                else:
                    cost = -np.log(1 - prob)
                
                total_cost += cost
            
            return total_cost / num_samples if num_samples > 0 else 1.0
            
        except Exception as e:
            self.logger.warning(f"Cost calculation failed: {e}")
            return 1.0
    
    def _calculate_convergence_rate(self) -> float:
        """Calculate convergence rate based on recent cost history."""
        if len(self.training_metrics) < 2:
            return 0.0
        
        recent_costs = [m.train_cost for m in self.training_metrics[-5:]]
        if len(recent_costs) < 2:
            return 0.0
        
        # Calculate rate of change
        cost_changes = [recent_costs[i] - recent_costs[i-1] for i in range(1, len(recent_costs))]
        return np.mean(np.abs(cost_changes))
    
    def _calculate_overfitting_score(self, train_cost: float, val_cost: float) -> float:
        """Calculate overfitting score (validation cost - training cost)."""
        return val_cost - train_cost
    
    def _training_loop(
        self,
        optimizer,
        cost_function: Callable,
        initial_parameters: np.ndarray,
        model_name: Optional[str]
    ) -> TrainingResult:
        """Main training loop with early stopping and checkpointing."""
        
        try:
            # Run optimization
            result = optimizer.minimize(
                fun=cost_function,
                x0=initial_parameters
            )
            
            # Check for early stopping
            early_stopped = self.patience_counter >= self.config.early_stopping_patience
            
            # Check for convergence
            convergence_achieved = (
                len(self.training_metrics) > 0 and
                self.training_metrics[-1].convergence_rate < self.config.early_stopping_threshold
            )
            
            # Check for overfitting
            overfitting_detected = (
                len(self.training_metrics) > 0 and
                self.training_metrics[-1].overfitting_score > 0.1  # Threshold for overfitting
            )
            
            # Get final costs
            final_train_cost = self.training_metrics[-1].train_cost if self.training_metrics else float('inf')
            final_val_cost = self.training_metrics[-1].val_cost if self.training_metrics else float('inf')
            
            return TrainingResult(
                success=result.success if hasattr(result, 'success') else True,
                final_parameters=self.best_parameters,
                training_metrics=self.training_metrics,
                best_iteration=len(self.training_metrics) - self.patience_counter,
                best_val_cost=self.best_val_cost,
                final_train_cost=final_train_cost,
                final_val_cost=final_val_cost,
                final_test_cost=None,  # Will be set later
                total_iterations=len(self.training_metrics),
                training_time=0,  # Will be set later
                convergence_achieved=convergence_achieved,
                early_stopped=early_stopped,
                overfitting_detected=overfitting_detected,
                model_path=None,  # Will be set later
                hyperparameters={}  # Will be set later
            )
            
        except Exception as e:
            self.logger.error("Training loop failed", error=str(e))
            raise
    
    def _evaluate_model(
        self,
        parameters: np.ndarray,
        features: np.ndarray,
        labels: np.ndarray
    ) -> float:
        """Evaluate model on test data."""
        return self._calculate_cost(parameters, features, labels)
    
    def _save_model(self, model_name: str, result: TrainingResult) -> str:
        """Save trained model and training results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{model_name}_{timestamp}.pkl"
        model_path = self.model_dir / model_filename
        
        model_data = {
            "parameters": result.final_parameters,
            "config": self.config,
            "training_result": result,
            "quantum_calculator_state": {
                "num_qubits": self.config.num_qubits,
                "num_layers": self.config.num_layers,
                "optimizer_type": self.config.optimizer_type.value
            }
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.logger.info(f"Model saved to {model_path}")
        return str(model_path)
    
    def load_model(self, model_path: str) -> Dict[str, Any]:
        """Load trained model from file."""
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        self.logger.info(f"Model loaded from {model_path}")
        return model_data
    
    def hyperparameter_optimization(
        self,
        train_data: pl.DataFrame,
        val_data: pl.DataFrame,
        param_grid: Dict[str, List[Any]],
        max_trials: int = 10
    ) -> Dict[str, Any]:
        """
        Perform hyperparameter optimization using grid search.
        
        Args:
            train_data: Training data
            val_data: Validation data
            param_grid: Grid of hyperparameters to search
            max_trials: Maximum number of trials
            
        Returns:
            Best hyperparameters and results
        """
        best_config = None
        best_val_cost = float('inf')
        results = []
        
        self.logger.info(
            "Starting hyperparameter optimization",
            param_grid=param_grid,
            max_trials=max_trials
        )
        
        # Generate parameter combinations
        import itertools
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        # Limit to max_trials
        param_combinations = param_combinations[:max_trials]
        
        for i, param_combo in enumerate(param_combinations):
            try:
                # Create config with current parameters
                config_dict = dict(zip(param_names, param_combo))
                config = TrainingConfig(**{**self.config.__dict__, **config_dict})
                
                # Create temporary trainer with new config
                temp_trainer = EnhancedQuantumTrainer(config)
                
                # Train model
                result = temp_trainer.train_model(
                    train_data, val_data, 
                    model_name=f"hyperparam_trial_{i}"
                )
                
                results.append({
                    "trial": i,
                    "config": config_dict,
                    "val_cost": result.best_val_cost,
                    "success": result.success
                })
                
                # Update best configuration
                if result.success and result.best_val_cost < best_val_cost:
                    best_val_cost = result.best_val_cost
                    best_config = config_dict
                
                self.logger.info(
                    f"Hyperparameter trial {i+1}/{len(param_combinations)} completed",
                    config=config_dict,
                    val_cost=result.best_val_cost,
                    success=result.success
                )
                
            except Exception as e:
                self.logger.error(f"Hyperparameter trial {i} failed", error=str(e))
                results.append({
                    "trial": i,
                    "config": dict(zip(param_names, param_combo)),
                    "val_cost": float('inf'),
                    "success": False,
                    "error": str(e)
                })
        
        self.logger.info(
            "Hyperparameter optimization completed",
            best_config=best_config,
            best_val_cost=best_val_cost,
            total_trials=len(results)
        )
        
        return {
            "best_config": best_config,
            "best_val_cost": best_val_cost,
            "all_results": results
        }
