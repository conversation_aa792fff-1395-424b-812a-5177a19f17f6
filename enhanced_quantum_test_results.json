{"overall_success": false, "tests_passed": 2, "total_tests": 8, "total_time": 12.528604984283447, "detailed_results": {"Multi-Source Data Fetching": {"success": true, "results": {"top_cryptos_count": 10, "specific_cryptos_count": 3, "multi_source_count": 5, "health_status": {"overall_healthy": true, "sources": {"coingecko": {"healthy": true, "available": true}, "ccxt": {"healthy": true, "available": true, "exchanges": ["binance", "coinbase", "kraken", "bybit"]}, "yfinance": {"healthy": true, "available": true}}}, "data_sources_available": ["coingecko"]}, "timestamp": **********.747227}, "Data Validation & Preprocessing": {"success": true, "results": {"validation_success": false, "original_rows": 2, "processed_rows": 4, "removed_rows": 1, "issues_count": 2, "warnings_count": 0, "statistics": {"original_rows": 5, "final_rows": 4, "data_retention_rate": 0.8, "columns": 16, "feature_columns": 6, "missing_values": {}, "numeric_statistics": {"price": {"mean": 13257.875, "std": 24535.117037880624, "min": 1.5, "max": 50000.0}, "price_change_percentage_24h": {"mean": 29.5, "std": 47.51490994063512, "min": -2.0, "max": 100.0}, "volume_24h": {"mean": 402500000.0, "std": 451691266.2427734, "min": 10000000.0, "max": **********.0}, "market_cap": {"mean": 340000000000.0, "std": 458330303020.28546, "min": **********0.0, "max": **********000.0}, "market_cap_rank": {"mean": 5.75, "std": 6.3966136874651625, "min": 1.0, "max": 15.0}, "volume_to_market_cap_ratio": {"mean": 0.12500000000000003, "std": 0.06454972243679027, "min": 0.05, "max": 0.2}, "price_volatility": {"mean": 30.5, "std": 46.665476175291154, "min": 2.0, "max": 100.0}, "volume_trend": {"mean": 1.3416666666666668, "std": 1.5056375541425782, "min": 0.03333333333333333, "max": 3.3333333333333335}, "market_momentum": {"mean": 4.5125, "std": 7.133299260043233, "min": -0.2, "max": 15.0}, "market_cap_rank_normalized": {"mean": 0.6166666666666667, "std": 0.4264409124976775, "min": 0.0, "max": 0.9333333333333333}, "price_change_percentage_24h_normalized": {"mean": 0.3088235294117647, "std": 0.46583245039838356, "min": 0.0, "max": 1.0}, "volume_to_market_cap_ratio_normalized": {"mean": 0.49999999999999994, "std": 0.4303314829119352, "min": 0.0, "max": 1.0}, "price_volatility_normalized": {"mean": 0.2908163265306122, "std": 0.4761783283192975, "min": 0.0, "max": 0.9999999999999999}, "volume_trend_normalized": {"mean": 0.3964646464646464, "std": 0.45625380428562967, "min": 0.0, "max": 0.9999999999999999}, "market_momentum_normalized": {"mean": 0.31003289473684204, "std": 0.46929600395021265, "min": 0.0, "max": 0.9999999999999999}}}}, "timestamp": **********.754826}, "Enhanced Quantum Training": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": **********.755531}, "Quantum Score Generation with Uncertainty": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": **********.756284}, "Portfolio Analysis": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": **********.756671}, "API Endpoints": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": 1751997546.0073862}, "System Integration": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": 1751997546.0077848}, "Performance & Scalability": {"success": false, "results": {"error": "No module named 'qiskit.algorithms'"}, "timestamp": 1751997546.008127}}}