#!/usr/bin/env python3
"""
Database Infrastructure Testing
Tests database initialization, table creation, data models, and CRUD operations.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import json

# Add the app directory to the path
sys.path.insert(0, str(Path('.').absolute()))

async def test_database():
    from app.core.database import init_database, db_manager
    from app.models.market_data import MarketData, TokenMetrics
    from app.models.scraper_data import ScraperRun, ScraperMetrics
    from sqlalchemy import text
    
    print('=== DATABASE INITIALIZATION ===')
    try:
        await init_database()
        print('✓ Database initialized successfully')
    except Exception as e:
        print(f'✗ Database initialization failed: {e}')
        return
    
    print('\n=== DATABASE CONNECTION TEST ===')
    try:
        async for session in db_manager.get_session():
            result = await session.execute(text('SELECT 1'))
            value = result.scalar()
            if value == 1:
                print('✓ Database connection successful')
            else:
                print('✗ Database connection failed')
            break
    except Exception as e:
        print(f'✗ Database connection error: {e}')
        return
    
    print('\n=== TABLE CREATION TEST ===')
    try:
        async for session in db_manager.get_session():
            # Test table creation with sample data
            await session.execute(text('''
                CREATE TABLE IF NOT EXISTS test_tokens (
                    id INTEGER PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    name TEXT NOT NULL,
                    price REAL NOT NULL,
                    market_cap REAL,
                    volume_24h REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''))
            print('✓ Test table created successfully')
            
            # Insert test data
            test_data = [
                ('BTC', 'Bitcoin', 45000.0, 850000000000.0, 25000000000.0),
                ('ETH', 'Ethereum', 3000.0, 360000000000.0, 15000000000.0),
                ('SOL', 'Solana', 100.0, 45000000000.0, 2000000000.0)
            ]
            
            for symbol, name, price, market_cap, volume in test_data:
                await session.execute(
                    'INSERT INTO test_tokens (symbol, name, price, market_cap, volume_24h) VALUES (?, ?, ?, ?, ?)',
                    (symbol, name, price, market_cap, volume)
                )
            
            await session.commit()
            print('✓ Test data inserted successfully')
            
            # Query test data
            result = await session.execute('SELECT * FROM test_tokens ORDER BY market_cap DESC')
            rows = result.fetchall()
            
            print(f'✓ Retrieved {len(rows)} records from test table')
            for row in rows:
                print(f'  {row.symbol}: ${row.price:,.2f} (Market Cap: ${row.market_cap:,.0f})')
            
            # Clean up test table
            await session.execute('DROP TABLE test_tokens')
            await session.commit()
            print('✓ Test table cleaned up')
            break

    except Exception as e:
        print(f'✗ Table operations failed: {e}')
    
    print('\n=== DATABASE FILE VALIDATION ===')
    db_path = Path('./data/alpha_grid.db')
    if db_path.exists():
        size = db_path.stat().st_size
        print(f'✓ Database file exists: {db_path} ({size} bytes)')
    else:
        print(f'✗ Database file not found: {db_path}')
    
    print('\n=== DATABASE SCHEMA VALIDATION ===')
    try:
        async for session in db_manager.get_session():
            # Check if main tables exist
            result = await session.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            ''')
            tables = [row[0] for row in result.fetchall()]
            
            if tables:
                print(f'✓ Found {len(tables)} tables in database:')
                for table in tables:
                    print(f'  - {table}')
            else:
                print('ℹ No user tables found (this is normal for a fresh database)')
            break

    except Exception as e:
        print(f'✗ Schema validation failed: {e}')
    
    # Close database connections
    await db_manager.close()
    print('\n✓ Database connections closed')

if __name__ == "__main__":
    asyncio.run(test_database())
