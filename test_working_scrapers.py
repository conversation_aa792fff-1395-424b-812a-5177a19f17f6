#!/usr/bin/env python3
"""
Test Working Scrapers with Real Data
Focus on scrapers that are properly integrated and working.
"""

import asyncio
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, str(Path('.').absolute()))

async def test_coingecko_comprehensive():
    """Comprehensive test of CoinGecko scraper."""
    print('\n=== COMPREHENSIVE COINGECKO TEST ===')
    
    try:
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        scraper = CoinGeckoScraper()
        results = {}
        
        # Test 1: Health check
        print('1. Testing health check...')
        health = await scraper.health_check()
        results['health_check'] = health
        print(f'   ✓ Health: {"PASS" if health else "FAIL"}')
        
        # Test 2: Market data for specific coins
        print('2. Testing market data for specific coins...')
        coin_ids = ["bitcoin", "ethereum", "solana", "cardano", "polkadot"]
        market_data = await scraper.fetch_market_data(coin_ids)
        results['market_data'] = {
            'count': len(market_data),
            'coins': [coin['symbol'].upper() for coin in market_data] if market_data else []
        }
        print(f'   ✓ Fetched data for {len(market_data)} coins: {", ".join(results["market_data"]["coins"])}')
        
        # Test 3: Top coins by market cap
        print('3. Testing top coins by market cap...')
        top_coins = await scraper.get_top_coins(limit=15)
        results['top_coins'] = {
            'count': len(top_coins),
            'top_5': [f"{coin['symbol']} (${coin['current_price']:,.2f})" for coin in top_coins[:5]] if top_coins else []
        }
        print(f'   ✓ Top 5 coins: {", ".join(results["top_coins"]["top_5"])}')
        
        # Test 4: Trending coins
        print('4. Testing trending coins...')
        trending = await scraper.get_trending_coins()
        results['trending'] = {
            'count': len(trending),
            'top_3': [f"{coin['name']} ({coin['symbol']})" for coin in trending[:3]] if trending else []
        }
        print(f'   ✓ Top 3 trending: {", ".join(results["trending"]["top_3"])}')
        
        # Test 5: Global market data
        print('5. Testing global market data...')
        global_data = await scraper.get_global_data()
        if global_data:
            total_mcap = global_data.get('total_market_cap', 0)
            total_volume = global_data.get('total_volume', 0)
            btc_dominance = global_data.get('market_cap_percentage', {}).get('btc', 0)
            results['global_data'] = {
                'total_market_cap': total_mcap,
                'total_volume': total_volume,
                'btc_dominance': btc_dominance
            }
            print(f'   ✓ Total Market Cap: ${total_mcap:,.0f}')
            print(f'   ✓ 24h Volume: ${total_volume:,.0f}')
            print(f'   ✓ BTC Dominance: {btc_dominance:.1f}%')
        
        # Test 6: Search functionality
        print('6. Testing search functionality...')
        search_results = await scraper.search_coins("bitcoin")
        results['search'] = {
            'query': 'bitcoin',
            'results_count': len(search_results),
            'first_result': search_results[0]['name'] if search_results else None
        }
        print(f'   ✓ Search for "bitcoin": {len(search_results)} results, first: {results["search"]["first_result"]}')
        
        # Test 7: Detailed coin information
        print('7. Testing detailed coin information...')
        btc_details = await scraper.get_coin_by_id("bitcoin")
        if btc_details:
            results['coin_details'] = {
                'name': btc_details.get('name'),
                'symbol': btc_details.get('symbol'),
                'current_price': btc_details.get('current_price'),
                'market_cap_rank': btc_details.get('market_cap_rank'),
                'ath': btc_details.get('ath'),
                'atl': btc_details.get('atl')
            }
            print(f'   ✓ BTC Details: {btc_details["name"]} (#{btc_details["market_cap_rank"]})')
            print(f'   ✓ Current Price: ${btc_details["current_price"]:,.2f}')
            print(f'   ✓ ATH: ${btc_details["ath"]:,.2f}, ATL: ${btc_details["atl"]:,.8f}')
        
        # Test 8: Performance metrics
        print('8. Testing scraper performance metrics...')
        metrics = await scraper.get_metrics()
        results['performance'] = {
            'total_requests': metrics['request_count'],
            'success_rate': metrics['success_rate'],
            'avg_response_time_ms': metrics['avg_response_time_ms'],
            'cache_hit_rate': metrics['cache_hit_rate']
        }
        print(f'   ✓ Total Requests: {metrics["request_count"]}')
        print(f'   ✓ Success Rate: {metrics["success_rate"]:.1%}')
        print(f'   ✓ Avg Response Time: {metrics["avg_response_time_ms"]:.0f}ms')
        print(f'   ✓ Cache Hit Rate: {metrics["cache_hit_rate"]:.1%}')
        
        await scraper.close()
        
        # Summary
        print('\n   === COINGECKO TEST SUMMARY ===')
        print(f'   ✓ All 8 test categories completed successfully')
        print(f'   ✓ Fetched data for {results["market_data"]["count"]} specific coins')
        print(f'   ✓ Retrieved {results["top_coins"]["count"]} top coins by market cap')
        print(f'   ✓ Found {results["trending"]["count"]} trending coins')
        print(f'   ✓ Global market data with ${results["global_data"]["total_market_cap"]:,.0f} total market cap')
        print(f'   ✓ Search functionality working with {results["search"]["results_count"]} results')
        print(f'   ✓ Detailed coin information for Bitcoin')
        print(f'   ✓ Performance metrics: {results["performance"]["success_rate"]:.1%} success rate')
        
        return True, results
        
    except Exception as e:
        print(f'   ✗ CoinGecko comprehensive test failed: {e}')
        return False, {}

async def test_etherscan_basic():
    """Basic test of Etherscan scraper functionality."""
    print('\n=== BASIC ETHERSCAN TEST ===')
    
    try:
        from app.agents.scrapers.etherscan_scraper import EtherscanScraper
        
        scraper = EtherscanScraper()
        results = {}
        
        # Test 1: Health check
        print('1. Testing health check...')
        health = await scraper.health_check()
        results['health_check'] = health
        print(f'   ✓ Health: {"PASS" if health else "FAIL"}')
        
        # Test 2: Account balance for Vitalik's address
        print('2. Testing account balance...')
        vitalik_address = "******************************************"
        balance = await scraper.get_account_balance(vitalik_address)
        if balance:
            results['balance'] = {
                'address': vitalik_address,
                'balance_eth': balance['balance_eth'],
                'balance_wei': balance['balance_wei']
            }
            print(f'   ✓ Vitalik\'s balance: {balance["balance_eth"]:.4f} ETH')
        
        # Test 3: Transaction history
        print('3. Testing transaction history...')
        transactions = await scraper.get_transaction_history(vitalik_address, offset=5)
        if transactions:
            results['transactions'] = {
                'count': len(transactions),
                'latest_hash': transactions[0]['hash'][:10] + '...' if transactions else None,
                'latest_value_eth': transactions[0]['value_eth'] if transactions else 0
            }
            print(f'   ✓ Retrieved {len(transactions)} recent transactions')
            print(f'   ✓ Latest tx: {results["transactions"]["latest_hash"]} ({results["transactions"]["latest_value_eth"]:.4f} ETH)')
        
        # Test 4: Performance metrics
        print('4. Testing performance metrics...')
        metrics = await scraper.get_metrics()
        results['performance'] = {
            'success_rate': metrics['success_rate'],
            'avg_response_time_ms': metrics['avg_response_time_ms']
        }
        print(f'   ✓ Success Rate: {metrics["success_rate"]:.1%}')
        print(f'   ✓ Avg Response Time: {metrics["avg_response_time_ms"]:.0f}ms')
        
        await scraper.close()
        
        print('\n   === ETHERSCAN TEST SUMMARY ===')
        print(f'   ✓ Health check: {"PASS" if results["health_check"] else "FAIL"}')
        print(f'   ✓ Balance check: {results["balance"]["balance_eth"]:.4f} ETH')
        print(f'   ✓ Transaction history: {results["transactions"]["count"]} transactions')
        print(f'   ✓ Performance: {results["performance"]["success_rate"]:.1%} success rate')
        
        return True, results
        
    except Exception as e:
        print(f'   ✗ Etherscan basic test failed: {e}')
        return False, {}

async def test_data_quality():
    """Test data quality and consistency."""
    print('\n=== DATA QUALITY VALIDATION ===')
    
    try:
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        scraper = CoinGeckoScraper()
        
        # Get market data for analysis
        print('1. Fetching market data for quality analysis...')
        market_data = await scraper.get_market_data(per_page=50, page=1)
        
        if not market_data:
            print('   ✗ No market data received')
            return False, {}
        
        # Data quality checks
        quality_results = {
            'total_coins': len(market_data),
            'complete_records': 0,
            'missing_price': 0,
            'missing_market_cap': 0,
            'missing_volume': 0,
            'negative_values': 0,
            'price_range': {'min': float('inf'), 'max': 0},
            'market_cap_range': {'min': float('inf'), 'max': 0}
        }
        
        print('2. Analyzing data quality...')
        for coin in market_data:
            # Check for complete records
            required_fields = ['id', 'symbol', 'name', 'current_price', 'market_cap', 'total_volume']
            if all(field in coin and coin[field] is not None for field in required_fields):
                quality_results['complete_records'] += 1
            
            # Check for missing data
            if not coin.get('current_price'):
                quality_results['missing_price'] += 1
            if not coin.get('market_cap'):
                quality_results['missing_market_cap'] += 1
            if not coin.get('total_volume'):
                quality_results['missing_volume'] += 1
            
            # Check for negative values
            price = coin.get('current_price', 0)
            market_cap = coin.get('market_cap', 0)
            volume = coin.get('total_volume', 0)
            
            if any(val < 0 for val in [price, market_cap, volume] if val is not None):
                quality_results['negative_values'] += 1
            
            # Track ranges
            if price and price > 0:
                quality_results['price_range']['min'] = min(quality_results['price_range']['min'], price)
                quality_results['price_range']['max'] = max(quality_results['price_range']['max'], price)
            
            if market_cap and market_cap > 0:
                quality_results['market_cap_range']['min'] = min(quality_results['market_cap_range']['min'], market_cap)
                quality_results['market_cap_range']['max'] = max(quality_results['market_cap_range']['max'], market_cap)
        
        # Calculate quality metrics
        completeness_rate = quality_results['complete_records'] / quality_results['total_coins']
        
        print('3. Data quality results:')
        print(f'   ✓ Total coins analyzed: {quality_results["total_coins"]}')
        print(f'   ✓ Complete records: {quality_results["complete_records"]} ({completeness_rate:.1%})')
        print(f'   ✓ Missing price data: {quality_results["missing_price"]} coins')
        print(f'   ✓ Missing market cap: {quality_results["missing_market_cap"]} coins')
        print(f'   ✓ Missing volume data: {quality_results["missing_volume"]} coins')
        print(f'   ✓ Negative values: {quality_results["negative_values"]} coins')
        print(f'   ✓ Price range: ${quality_results["price_range"]["min"]:.8f} - ${quality_results["price_range"]["max"]:,.2f}')
        print(f'   ✓ Market cap range: ${quality_results["market_cap_range"]["min"]:,.0f} - ${quality_results["market_cap_range"]["max"]:,.0f}')
        
        await scraper.close()
        
        # Quality assessment
        is_high_quality = (
            completeness_rate >= 0.9 and  # 90%+ complete records
            quality_results['negative_values'] == 0 and  # No negative values
            quality_results['missing_price'] < quality_results['total_coins'] * 0.1  # <10% missing prices
        )
        
        print(f'\n   === DATA QUALITY ASSESSMENT ===')
        print(f'   {"✓" if is_high_quality else "⚠"} Overall Quality: {"HIGH" if is_high_quality else "MEDIUM"}')
        print(f'   ✓ Data completeness: {completeness_rate:.1%}')
        print(f'   ✓ Data integrity: {"GOOD" if quality_results["negative_values"] == 0 else "ISSUES FOUND"}')
        
        return True, quality_results
        
    except Exception as e:
        print(f'   ✗ Data quality validation failed: {e}')
        return False, {}

async def main():
    """Run comprehensive scraper validation."""
    print('=== COMPREHENSIVE SCRAPER VALIDATION ===')
    print(f'Test started at: {datetime.now().isoformat()}')
    
    start_time = time.time()
    all_results = {}
    
    # Test 1: CoinGecko comprehensive test
    coingecko_success, coingecko_results = await test_coingecko_comprehensive()
    all_results['coingecko'] = {'success': coingecko_success, 'data': coingecko_results}
    
    # Test 2: Etherscan basic test
    etherscan_success, etherscan_results = await test_etherscan_basic()
    all_results['etherscan'] = {'success': etherscan_success, 'data': etherscan_results}
    
    # Test 3: Data quality validation
    quality_success, quality_results = await test_data_quality()
    all_results['data_quality'] = {'success': quality_success, 'data': quality_results}
    
    # Final summary
    total_time = time.time() - start_time
    successful_tests = sum(1 for result in all_results.values() if result['success'])
    total_tests = len(all_results)
    
    print(f'\n=== FINAL VALIDATION SUMMARY ===')
    print(f'Total test suites: {total_tests}')
    print(f'Successful: {successful_tests}')
    print(f'Failed: {total_tests - successful_tests}')
    print(f'Success rate: {successful_tests/total_tests*100:.1f}%')
    print(f'Total test time: {total_time:.2f} seconds')
    
    for test_name, result in all_results.items():
        status = "✓ PASS" if result['success'] else "✗ FAIL"
        print(f'  {test_name.title()}: {status}')
    
    # Save detailed results
    with open('scraper_validation_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f'\nDetailed results saved to: scraper_validation_results.json')

if __name__ == "__main__":
    asyncio.run(main())
