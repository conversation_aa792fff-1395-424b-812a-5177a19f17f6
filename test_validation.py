#!/usr/bin/env python3
"""
Data accuracy and consistency validation for Alpha Grid system.
"""

import asyncio
import json
from datetime import datetime, timedelta
from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
from quantum_scoring import QuantumScoreCalculator
from app.core.database import init_database, db_manager
from sqlalchemy import text
import numpy as np

async def validate_data_accuracy():
    """Comprehensive data accuracy and consistency validation."""
    print('Data Accuracy and Consistency Validation')
    print('=' * 50)
    
    # Initialize components
    await init_database()
    scraper = CoinGeckoScraper()
    calc = QuantumScoreCalculator()
    
    # Test 1: Market data accuracy validation
    print('\n1. Validating market data accuracy...')
    
    # Fetch data for well-known cryptocurrencies
    market_data = await scraper.fetch_market_data(['bitcoin', 'ethereum', 'tether'])
    
    validation_results = []
    
    for coin in market_data:
        symbol = coin['symbol'].upper()
        price = coin.get('current_price', 0)
        market_cap = coin.get('market_cap', 0)
        volume = coin.get('total_volume', 0)
        change_24h = coin.get('price_change_percentage_24h', 0)
        
        # Validation checks
        checks = {
            'price_positive': price > 0,
            'price_reasonable': 0.000001 < price < 10000000,  # Reasonable price range
            'market_cap_positive': market_cap > 0,
            'volume_positive': volume >= 0,
            'change_percentage_range': -100 <= change_24h <= 1000,  # Reasonable 24h change
            'market_cap_volume_ratio': market_cap >= volume if volume > 0 else True,
            'symbol_format': len(symbol) >= 2 and symbol.isalpha()
        }
        
        all_passed = all(checks.values())
        validation_results.append({
            'symbol': symbol,
            'price': price,
            'checks': checks,
            'passed': all_passed
        })
        
        status = '✓' if all_passed else '✗'
        print(f'{status} {symbol:6}: Price=${price:12.6f}, MCap=${market_cap:15,.0f}')
    
    passed_validations = sum(1 for r in validation_results if r['passed'])
    print(f'\nMarket data validation: {passed_validations}/{len(validation_results)} passed')
    
    # Test 2: Quantum scoring consistency
    print('\n2. Testing quantum scoring consistency...')
    
    # Test with same input multiple times
    test_features = {
        'social_momentum': 0.75,
        'onchain_activity': 0.60,
        'technical_strength': 0.80,
        'volume_momentum': 0.70,
        'holder_growth': 0.65
    }
    
    scores = []
    for i in range(10):
        score = calc.calculate(test_features)
        scores.append(score)
    
    score_variance = np.var(scores)
    score_mean = np.mean(scores)
    
    print(f'Score consistency: mean={score_mean:.6f}, variance={score_variance:.8f}')
    print(f'Consistency check: {"✓ PASS" if score_variance < 0.001 else "✗ FAIL"} (variance < 0.001)')
    
    # Test 3: Feature sensitivity analysis
    print('\n3. Testing feature sensitivity...')
    
    base_features = test_features.copy()
    sensitivities = {}
    
    for feature_name in base_features.keys():
        # Test with feature at 0 and 1
        features_min = base_features.copy()
        features_max = base_features.copy()
        features_min[feature_name] = 0.0
        features_max[feature_name] = 1.0
        
        score_min = calc.calculate(features_min)
        score_max = calc.calculate(features_max)
        
        sensitivity = abs(score_max - score_min)
        sensitivities[feature_name] = sensitivity
        
        print(f'{feature_name:18}: sensitivity={sensitivity:.6f}')
    
    # Test 4: Cross-validation with historical data patterns
    print('\n4. Cross-validating with expected patterns...')
    
    # Create test scenarios with known expected behaviors
    test_scenarios = [
        {
            'name': 'Bullish scenario',
            'features': {'social_momentum': 0.9, 'onchain_activity': 0.8, 'technical_strength': 0.85, 'volume_momentum': 0.9, 'holder_growth': 0.8},
            'expected_range': (0.5, 1.0)
        },
        {
            'name': 'Bearish scenario', 
            'features': {'social_momentum': 0.2, 'onchain_activity': 0.3, 'technical_strength': 0.1, 'volume_momentum': 0.2, 'holder_growth': 0.1},
            'expected_range': (0.0, 0.5)
        },
        {
            'name': 'Neutral scenario',
            'features': {'social_momentum': 0.5, 'onchain_activity': 0.5, 'technical_strength': 0.5, 'volume_momentum': 0.5, 'holder_growth': 0.5},
            'expected_range': (0.1, 0.9)
        }
    ]
    
    scenario_results = []
    for scenario in test_scenarios:
        score = calc.calculate(scenario['features'])
        min_expected, max_expected = scenario['expected_range']
        in_range = min_expected <= score <= max_expected
        
        scenario_results.append({
            'name': scenario['name'],
            'score': score,
            'expected_range': scenario['expected_range'],
            'in_range': in_range
        })
        
        status = '✓' if in_range else '✗'
        print(f'{status} {scenario["name"]:15}: score={score:.3f}, expected={scenario["expected_range"]}')
    
    scenarios_passed = sum(1 for r in scenario_results if r['in_range'])
    
    # Test 5: Database data integrity
    print('\n5. Testing database data integrity...')
    
    async for session in db_manager.get_session():
        # Insert test data with known values
        test_records = [
            {'symbol': 'TEST1', 'price': 100.0, 'market_cap': 1000000, 'volume_24h': 50000, 'data_source': 'validation_test'},
            {'symbol': 'TEST2', 'price': 0.50, 'market_cap': 500000, 'volume_24h': 25000, 'data_source': 'validation_test'},
            {'symbol': 'TEST3', 'price': 1000.0, 'market_cap': 10000000, 'volume_24h': 100000, 'data_source': 'validation_test'}
        ]
        
        # Create test table
        await session.execute(text('''
            CREATE TABLE IF NOT EXISTS validation_test_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL,
                market_cap REAL,
                volume_24h REAL,
                data_source TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''))
        
        # Insert test records
        for record in test_records:
            await session.execute(text('''
                INSERT INTO validation_test_data (symbol, price, market_cap, volume_24h, data_source)
                VALUES (:symbol, :price, :market_cap, :volume_24h, :data_source)
            '''), record)
        
        await session.commit()
        
        # Retrieve and validate
        result = await session.execute(text('''
            SELECT symbol, price, market_cap, volume_24h 
            FROM validation_test_data 
            WHERE data_source = :data_source
            ORDER BY symbol
        '''), {'data_source': 'validation_test'})
        
        retrieved_records = result.fetchall()
        
        integrity_checks = []
        for i, (original, retrieved) in enumerate(zip(test_records, retrieved_records)):
            symbol, price, market_cap, volume = retrieved
            
            price_match = abs(float(price) - original['price']) < 0.001
            mcap_match = abs(float(market_cap) - original['market_cap']) < 1
            volume_match = abs(float(volume) - original['volume_24h']) < 1
            symbol_match = symbol == original['symbol']
            
            all_match = price_match and mcap_match and volume_match and symbol_match
            integrity_checks.append(all_match)
            
            status = '✓' if all_match else '✗'
            print(f'{status} Record {i+1}: {symbol}, price_match={price_match}, mcap_match={mcap_match}')
        
        # Clean up test data
        await session.execute(text('DELETE FROM validation_test_data WHERE data_source = :data_source'), 
                            {'data_source': 'validation_test'})
        await session.commit()
        
        db_integrity_passed = all(integrity_checks)
    
    # Generate final validation report
    print('\n' + '=' * 50)
    print('VALIDATION SUMMARY')
    print('=' * 50)
    
    total_tests = 5
    passed_tests = 0
    
    # Count passed tests
    if passed_validations == len(validation_results):
        passed_tests += 1
        print('✓ Market data accuracy: PASS')
    else:
        print('✗ Market data accuracy: FAIL')
    
    if score_variance < 0.001:
        passed_tests += 1
        print('✓ Quantum scoring consistency: PASS')
    else:
        print('✗ Quantum scoring consistency: FAIL')
    
    if all(s > 0 for s in sensitivities.values()):
        passed_tests += 1
        print('✓ Feature sensitivity: PASS')
    else:
        print('✗ Feature sensitivity: FAIL')
    
    if scenarios_passed == len(test_scenarios):
        passed_tests += 1
        print('✓ Scenario validation: PASS')
    else:
        print('✗ Scenario validation: FAIL')
    
    if db_integrity_passed:
        passed_tests += 1
        print('✓ Database integrity: PASS')
    else:
        print('✗ Database integrity: FAIL')
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f'\nOVERALL VALIDATION: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)')
    print(f'SYSTEM STATUS: {"✓ VALIDATED" if passed_tests == total_tests else "⚠ ISSUES DETECTED"}')
    
    return {
        'market_data_validation': passed_validations == len(validation_results),
        'scoring_consistency': score_variance < 0.001,
        'feature_sensitivity': all(s > 0 for s in sensitivities.values()),
        'scenario_validation': scenarios_passed == len(test_scenarios),
        'database_integrity': db_integrity_passed,
        'overall_passed': passed_tests,
        'total_tests': total_tests,
        'success_rate': success_rate
    }

if __name__ == "__main__":
    asyncio.run(validate_data_accuracy())