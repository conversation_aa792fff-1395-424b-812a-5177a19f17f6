#!/usr/bin/env python3
"""
Quantum Computing System Validation
Tests quantum score calculator, feature encoder, and circuit optimizer with real market data.
"""

import asyncio
import sys
import time
import json
import numpy as np
import polars as pl
from pathlib import Path
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, str(Path('.').absolute()))

async def test_quantum_feature_encoder():
    """Test quantum feature encoder with real market data."""
    print('\n=== QUANTUM FEATURE ENCODER TEST ===')
    
    try:
        from app.agents.quantum.quantum_feature_encoder import QuantumFeatureEncoder
        
        encoder = QuantumFeatureEncoder(num_qubits=4, encoding_type="amplitude")
        
        # Test 1: Create sample market data
        print('1. Creating sample market data...')
        market_data = pl.DataFrame({
            'symbol': ['BTC', 'ETH', 'SOL', 'ADA', 'DOT'],
            'price_change_percentage_24h': [2.5, -1.2, 5.8, -0.5, 3.1],
            'volume_to_market_cap_ratio': [0.05, 0.08, 0.12, 0.03, 0.06],
            'market_cap_rank': [1, 2, 5, 10, 15],
            'circulating_supply': [19.8e6, 120.4e6, 470.2e6, 35.8e9, 1.4e9]
        })
        print(f'   ✓ Created market data for {len(market_data)} tokens')
        
        # Test 2: Extract features
        print('2. Testing feature extraction...')
        features = encoder.extract_market_features(market_data)
        print(f'   ✓ Extracted features shape: {features.shape}')
        print(f'   ✓ Feature range: [{features.min():.4f}, {features.max():.4f}]')
        
        # Test 3: Encode features into quantum circuits
        print('3. Testing quantum feature encoding...')
        quantum_circuits = encoder.encode_features(features)
        print(f'   ✓ Generated {len(quantum_circuits)} quantum circuits')
        
        if quantum_circuits:
            sample_circuit = quantum_circuits[0]
            print(f'   ✓ Sample circuit depth: {sample_circuit.depth()}')
            print(f'   ✓ Sample circuit size: {sample_circuit.size()}')
            print(f'   ✓ Number of qubits: {sample_circuit.num_qubits}')
        
        # Test 4: Get encoding statistics
        print('4. Testing encoding statistics...')
        stats = encoder.get_encoding_stats()
        print(f'   ✓ Encoding statistics: {stats}')
        
        return True, {
            'features_shape': features.shape,
            'feature_range': [float(features.min()), float(features.max())],
            'circuits_generated': len(quantum_circuits),
            'circuit_depth': sample_circuit.depth() if quantum_circuits else 0,
            'encoding_stats': stats
        }
        
    except Exception as e:
        print(f'   ✗ Quantum feature encoder test failed: {e}')
        return False, {}

async def test_quantum_circuit_optimizer():
    """Test quantum circuit optimizer."""
    print('\n=== QUANTUM CIRCUIT OPTIMIZER TEST ===')
    
    try:
        from app.agents.quantum.quantum_circuit_optimizer import QuantumCircuitOptimizer
        
        optimizer = QuantumCircuitOptimizer()
        
        # Test 1: Create test circuit
        print('1. Creating test circuit...')
        from qiskit import QuantumCircuit
        from qiskit.circuit.library import RealAmplitudes

        test_circuit = QuantumCircuit(4)
        test_circuit.h(0)
        test_circuit.cx(0, 1)
        test_circuit.cx(1, 2)
        test_circuit.cx(2, 3)
        test_circuit.ry(0.5, 0)
        test_circuit.ry(1.0, 1)
        print(f'   ✓ Created test circuit with {test_circuit.depth()} depth and {test_circuit.size()} gates')

        # Test 2: Optimize circuit
        print('2. Testing circuit optimization...')
        optimized_circuit, metrics = optimizer.optimize_circuit(test_circuit)
        print(f'   ✓ Optimized circuit depth: {optimized_circuit.depth()}')
        print(f'   ✓ Optimized circuit size: {optimized_circuit.size()}')
        print(f'   ✓ Depth improvement: {metrics["depth_improvement_percent"]:.1f}%')
        
        # Test 3: Get optimization statistics
        print('3. Testing optimization statistics...')
        stats = optimizer.get_optimization_statistics()
        print(f'   ✓ Optimization statistics: {stats}')
        
        return True, {
            'original_depth': test_circuit.depth(),
            'original_size': test_circuit.size(),
            'optimized_depth': optimized_circuit.depth(),
            'optimized_size': optimized_circuit.size(),
            'depth_improvement': metrics["depth_improvement_percent"],
            'optimization_stats': stats
        }
        
    except Exception as e:
        print(f'   ✗ Quantum circuit optimizer test failed: {e}')
        return False, {}

async def test_quantum_score_calculator():
    """Test quantum score calculator with real market data."""
    print('\n=== QUANTUM SCORE CALCULATOR TEST ===')
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        # Test 1: Initialize calculator
        print('1. Initializing quantum score calculator...')
        calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        print(f'   ✓ Calculator initialized with {calculator.num_qubits} qubits, {calculator.num_layers} layers')
        
        # Test 2: Get real market data
        print('2. Fetching real market data...')
        scraper = CoinGeckoScraper()
        market_data_raw = await scraper.get_market_data(per_page=20, page=1)
        await scraper.close()
        
        # Convert to Polars DataFrame
        market_data = pl.DataFrame(market_data_raw)
        print(f'   ✓ Fetched market data for {len(market_data)} tokens')
        
        # Test 3: Calculate quantum scores (without training)
        print('3. Testing quantum score calculation (untrained model)...')
        score_results = calculator.calculate_quantum_score(market_data)
        print(f'   ✓ Calculated scores for {score_results["num_samples"]} tokens')
        print(f'   ✓ Average score: {score_results["average_score"]:.4f}')
        print(f'   ✓ Score std dev: {score_results["score_std"]:.4f}')
        print(f'   ✓ Calculation time: {score_results["calculation_time"]:.3f}s')
        print(f'   ✓ Model trained: {score_results["model_trained"]}')
        
        # Test 4: Train quantum model with sample data
        print('4. Testing quantum model training...')
        training_start = time.time()
        training_results = calculator.train_quantum_model(
            market_data, 
            max_iterations=20,  # Reduced for testing
            target_column="price_change_percentage_24h"
        )
        training_time = time.time() - training_start
        
        print(f'   ✓ Training completed in {training_time:.2f}s')
        print(f'   ✓ Final cost: {training_results["final_cost"]:.6f}')
        print(f'   ✓ Training iterations: {training_results["iterations"]}')
        print(f'   ✓ Test cost: {training_results["test_cost"]:.6f}')
        
        # Test 5: Calculate scores with trained model
        print('5. Testing quantum score calculation (trained model)...')
        trained_score_results = calculator.calculate_quantum_score(market_data)
        print(f'   ✓ Trained model scores - Average: {trained_score_results["average_score"]:.4f}')
        print(f'   ✓ High confidence predictions: {trained_score_results["high_confidence_count"]}')
        print(f'   ✓ Model trained: {trained_score_results["model_trained"]}')
        
        # Test 6: Get model information
        print('6. Testing model information...')
        model_info = calculator.get_model_info()
        print(f'   ✓ Model parameters: {model_info["num_parameters"]}')
        print(f'   ✓ Training sessions: {model_info["training_sessions"]}')
        print(f'   ✓ Total scores calculated: {model_info["calculation_stats"]["scores_calculated"]}')
        
        # Test 7: Save and load model
        print('7. Testing model persistence...')
        model_path = calculator.save_model("test_quantum_model.pkl")
        print(f'   ✓ Model saved to: {model_path}')
        
        # Create new calculator and load model
        new_calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        load_success = new_calculator.load_model(model_path)
        print(f'   ✓ Model loaded successfully: {load_success}')
        
        if load_success:
            # Test loaded model
            loaded_scores = new_calculator.calculate_quantum_score(market_data.head(5))
            print(f'   ✓ Loaded model test - Average score: {loaded_scores["average_score"]:.4f}')
        
        return True, {
            'untrained_scores': {
                'average': score_results["average_score"],
                'std': score_results["score_std"],
                'calculation_time': score_results["calculation_time"]
            },
            'training': {
                'final_cost': training_results["final_cost"],
                'iterations': training_results["iterations"],
                'training_time': training_time
            },
            'trained_scores': {
                'average': trained_score_results["average_score"],
                'high_confidence': trained_score_results["high_confidence_count"]
            },
            'model_info': model_info,
            'persistence': {
                'save_success': bool(model_path),
                'load_success': load_success
            }
        }
        
    except Exception as e:
        print(f'   ✗ Quantum score calculator test failed: {e}')
        import traceback
        traceback.print_exc()
        return False, {}

async def test_quantum_integration():
    """Test integration between quantum components."""
    print('\n=== QUANTUM INTEGRATION TEST ===')
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        # Test 1: End-to-end pipeline
        print('1. Testing end-to-end quantum analysis pipeline...')
        
        # Get real market data
        scraper = CoinGeckoScraper()
        market_data_raw = await scraper.get_top_coins(limit=10)
        await scraper.close()
        
        # Convert to Polars DataFrame with required columns
        market_data = pl.DataFrame({
            'symbol': [coin['symbol'] for coin in market_data_raw],
            'price_change_percentage_24h': [coin.get('price_change_percentage_24h', 0) for coin in market_data_raw],
            'volume_to_market_cap_ratio': [coin.get('volume_to_market_cap_ratio', 0) for coin in market_data_raw],
            'market_cap_rank': [coin.get('market_cap_rank', 0) for coin in market_data_raw],
            'circulating_supply': [coin.get('circulating_supply', 0) for coin in market_data_raw]
        })
        
        print(f'   ✓ Prepared market data for {len(market_data)} top coins')
        
        # Initialize quantum calculator
        calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        
        # Calculate scores
        results = calculator.calculate_quantum_score(market_data)
        
        # Analyze results
        scores = results['quantum_scores']
        symbols = results.get('symbols', [f'COIN_{i}' for i in range(len(scores))])
        
        print(f'   ✓ Generated quantum scores for {len(scores)} coins')
        print(f'   ✓ Score range: [{min(scores):.4f}, {max(scores):.4f}]')
        
        # Top scoring coins
        score_pairs = list(zip(symbols, scores))
        score_pairs.sort(key=lambda x: x[1], reverse=True)
        
        print('   ✓ Top 5 quantum scores:')
        for i, (symbol, score) in enumerate(score_pairs[:5]):
            print(f'      {i+1}. {symbol}: {score:.4f}')
        
        return True, {
            'pipeline_success': True,
            'coins_analyzed': len(scores),
            'score_range': [min(scores), max(scores)],
            'top_scores': score_pairs[:5],
            'calculation_time': results['calculation_time']
        }
        
    except Exception as e:
        print(f'   ✗ Quantum integration test failed: {e}')
        return False, {}

async def main():
    """Run comprehensive quantum system validation."""
    print('=== QUANTUM COMPUTING SYSTEM VALIDATION ===')
    print(f'Test started at: {datetime.now().isoformat()}')
    
    start_time = time.time()
    all_results = {}
    
    # Test 1: Quantum Feature Encoder
    encoder_success, encoder_results = await test_quantum_feature_encoder()
    all_results['feature_encoder'] = {'success': encoder_success, 'data': encoder_results}
    
    # Test 2: Quantum Circuit Optimizer
    optimizer_success, optimizer_results = await test_quantum_circuit_optimizer()
    all_results['circuit_optimizer'] = {'success': optimizer_success, 'data': optimizer_results}
    
    # Test 3: Quantum Score Calculator
    calculator_success, calculator_results = await test_quantum_score_calculator()
    all_results['score_calculator'] = {'success': calculator_success, 'data': calculator_results}
    
    # Test 4: Quantum Integration
    integration_success, integration_results = await test_quantum_integration()
    all_results['integration'] = {'success': integration_success, 'data': integration_results}
    
    # Final summary
    total_time = time.time() - start_time
    successful_tests = sum(1 for result in all_results.values() if result['success'])
    total_tests = len(all_results)
    
    print(f'\n=== QUANTUM SYSTEM VALIDATION SUMMARY ===')
    print(f'Total test suites: {total_tests}')
    print(f'Successful: {successful_tests}')
    print(f'Failed: {total_tests - successful_tests}')
    print(f'Success rate: {successful_tests/total_tests*100:.1f}%')
    print(f'Total test time: {total_time:.2f} seconds')
    
    for test_name, result in all_results.items():
        status = "✓ PASS" if result['success'] else "✗ FAIL"
        print(f'  {test_name.replace("_", " ").title()}: {status}')
    
    # Save detailed results
    with open('quantum_validation_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f'\nDetailed results saved to: quantum_validation_results.json')

if __name__ == "__main__":
    asyncio.run(main())
