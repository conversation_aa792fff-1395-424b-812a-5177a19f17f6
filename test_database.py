#!/usr/bin/env python3
"""
Database testing with real data operations.
"""

import asyncio
from app.core.database import init_database, db_manager
from datetime import datetime
import json
from sqlalchemy import text

async def test_database():
    """Test database operations with real data."""
    # Initialize database
    await init_database()
    
    print('Testing Database Operations with Real Data:')
    print('=' * 60)
    
    # Test database health
    health = await db_manager.health_check()
    print(f'Database Health: {"✓" if health else "✗"}')
    
    # Test inserting market data
    async for session in db_manager.get_session():
        # Create test market data
        market_record = {
            'symbol': 'BTC',
            'price': 108000.0,
            'market_cap': 2150000000000,
            'volume_24h': 25000000000,
            'price_change_24h': -0.62,
            'data_source': 'test',
            'timestamp': datetime.now()
        }
        
        # Insert using raw SQL for now
        await session.execute(text('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL,
                market_cap REAL,
                volume_24h REAL,
                price_change_24h REAL,
                data_source TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''))
        
        await session.execute(text('''
            INSERT INTO market_data (symbol, price, market_cap, volume_24h, price_change_24h, data_source)
            VALUES (:symbol, :price, :market_cap, :volume_24h, :price_change_24h, :data_source)
        '''), market_record)
        
        # Insert scraper data
        await session.execute(text('''
            CREATE TABLE IF NOT EXISTS scraper_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scraper_name TEXT NOT NULL,
                data_type TEXT,
                raw_data TEXT,
                processed_data TEXT,
                status TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''))
        
        scraper_record = {
            'scraper_name': 'CoinGecko',
            'data_type': 'market_data',
            'raw_data': json.dumps({'bitcoin': {'price': 108000}}),
            'processed_data': json.dumps({'BTC': 108000}),
            'status': 'success'
        }
        
        await session.execute(text('''
            INSERT INTO scraper_data (scraper_name, data_type, raw_data, processed_data, status)
            VALUES (:scraper_name, :data_type, :raw_data, :processed_data, :status)
        '''), scraper_record)
        
        await session.commit()
        
        # Test data retrieval
        result = await session.execute(text('SELECT COUNT(*) FROM market_data'))
        market_count = result.scalar()
        
        result = await session.execute(text('SELECT COUNT(*) FROM scraper_data'))  
        scraper_count = result.scalar()
        
        # Test complex query
        result = await session.execute(text('''
            SELECT symbol, price, market_cap, price_change_24h 
            FROM market_data 
            WHERE symbol = :symbol 
            ORDER BY timestamp DESC LIMIT 1
        '''), {'symbol': 'BTC'})
        
        btc_data = result.fetchone()
        
        print(f'Market Data Records: {market_count}')
        print(f'Scraper Data Records: {scraper_count}')
        print(f'Latest BTC Data: {btc_data}')
        
        # Test data accuracy
        if btc_data:
            symbol, price, mcap, change = btc_data
            print(f'Data Validation:')
            print(f'  Symbol: {symbol} (✓ if BTC)')
            print(f'  Price: ${price:,.2f}')
            print(f'  Market Cap: ${mcap:,.0f}')
            print(f'  24h Change: {change:+.2f}%')
        
        # Test concurrent operations
        print('\nTesting concurrent database operations...')
        
        # Insert multiple records concurrently
        symbols = ['ETH', 'SOL', 'ADA', 'DOT']
        prices = [2550.0, 150.0, 0.58, 3.33]
        
        tasks = []
        for symbol, price in zip(symbols, prices):
            task = session.execute(text('''
                INSERT INTO market_data (symbol, price, market_cap, volume_24h, price_change_24h, data_source)
                VALUES (:symbol, :price, :market_cap, :volume_24h, :price_change_24h, :data_source)
            '''), {
                'symbol': symbol,
                'price': price,
                'market_cap': price * 1000000000,
                'volume_24h': price * 100000000,
                'price_change_24h': -1.5,
                'data_source': 'test_concurrent'
            })
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        await session.commit()
        
        # Verify concurrent inserts
        result = await session.execute(text('SELECT COUNT(*) FROM market_data WHERE data_source = :data_source'), {'data_source': 'test_concurrent'})
        concurrent_count = result.scalar()
        print(f'Concurrent inserts: {concurrent_count} records')
        
        # Clean up test data
        await session.execute(text('DELETE FROM market_data WHERE data_source IN (:test, :test_concurrent)'), {'test': 'test', 'test_concurrent': 'test_concurrent'})
        await session.execute(text('DELETE FROM scraper_data WHERE scraper_name = :scraper_name'), {'scraper_name': 'CoinGecko'})
        await session.commit()
        
        print('Database operations completed successfully!')

if __name__ == "__main__":
    asyncio.run(test_database())