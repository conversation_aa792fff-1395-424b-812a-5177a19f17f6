"""
Main application entry point for Alpha Grid API.
"""

import uvicorn
from app.api.app import create_app
from app.core.config import settings

# Create FastAPI application
app = create_app()

if __name__ == "__main__":
    # Run with uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )