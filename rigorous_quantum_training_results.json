{"success": true, "data_samples": 8, "training_iterations": 1, "convergence_achieved": true, "quantum_advantage": -1.0, "training_metrics": {"training_time": 6.954355955123901, "iterations": 1, "convergence": true, "final_cost": 0.539554692206851}, "validation_metrics": {"accuracy": 0.0, "precision": 0, "recall": 0.0, "f1_score": 0, "threshold": 0.560302734375}, "test_metrics": {"accuracy": 0.0, "precision": 0.0, "recall": 0.0, "f1_score": 0, "threshold": 0.6993408203125}, "classical_baseline": {"validation": {"accuracy": 0.0, "precision": 0, "recall": 0.0, "f1_score": 0, "threshold": 0.507602928821924}, "test": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "threshold": 0.331392346592228}}}