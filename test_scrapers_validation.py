#!/usr/bin/env python3
"""
Data Scraper Validation with Live APIs
Tests all data scrapers with real API calls and validates data quality.
"""

import asyncio
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add the app directory to the path
sys.path.insert(0, str(Path('.').absolute()))

async def test_coingecko_scraper():
    """Test CoinGecko scraper with real API calls."""
    print('\n=== COINGECKO SCRAPER TEST ===')
    
    try:
        from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
        
        scraper = CoinGeckoScraper()
        
        # Test health check
        print('Testing health check...')
        health = await scraper.health_check()
        print(f'✓ Health check: {"PASS" if health else "FAIL"}')
        
        # Test market data fetching
        print('Testing market data fetching...')
        start_time = time.time()
        market_data = await scraper.get_market_data(per_page=10, page=1)
        fetch_time = time.time() - start_time
        
        if market_data and len(market_data) > 0:
            print(f'✓ Market data fetched: {len(market_data)} coins in {fetch_time:.2f}s')
            
            # Validate data structure
            sample = market_data[0]
            required_fields = ['id', 'symbol', 'current_price', 'market_cap', 'total_volume']
            missing_fields = [field for field in required_fields if field not in sample]
            
            if missing_fields:
                print(f'✗ Missing required fields: {missing_fields}')
            else:
                print('✓ Data structure validation passed')
                print(f'  Sample: {sample["symbol"]} - ${sample["current_price"]:,.2f}')
        else:
            print('✗ No market data received')
        
        # Test trending coins
        print('Testing trending coins...')
        trending = await scraper.get_trending_coins()
        if trending:
            print(f'✓ Trending coins fetched: {len(trending)} coins')
            print(f'  Top trending: {trending[0]["name"]} ({trending[0]["symbol"]})')
        else:
            print('✗ No trending data received')
        
        # Test global data
        print('Testing global market data...')
        global_data = await scraper.get_global_data()
        if global_data and 'total_market_cap' in global_data:
            market_cap = global_data['total_market_cap']
            print(f'✓ Global data fetched: Total market cap ${market_cap:,.0f}')
        else:
            print('✗ No global data received')
        
        # Test metrics
        metrics = await scraper.get_metrics()
        print(f'✓ Scraper metrics: {metrics["success_rate"]:.1%} success rate, {metrics["avg_response_time_ms"]:.0f}ms avg response')
        
        await scraper.close()
        return True
        
    except Exception as e:
        print(f'✗ CoinGecko scraper test failed: {e}')
        return False

async def test_etherscan_scraper():
    """Test Etherscan scraper with real API calls."""
    print('\n=== ETHERSCAN SCRAPER TEST ===')
    
    try:
        from app.agents.scrapers.etherscan_scraper import EtherscanScraper
        
        scraper = EtherscanScraper()
        
        # Test health check
        print('Testing health check...')
        health = await scraper.health_check()
        print(f'✓ Health check: {"PASS" if health else "FAIL"}')
        
        # Test gas prices
        print('Testing gas price fetching...')
        gas_data = await scraper.get_gas_price()
        if gas_data and 'SafeGasPrice' in gas_data:
            print(f'✓ Gas prices fetched: Safe={gas_data["SafeGasPrice"]} gwei')
        else:
            print('✗ No gas price data received')
        
        # Test account balance for a known address (Vitalik's address)
        print('Testing account balance...')
        vitalik_address = "******************************************"
        balance = await scraper.get_account_balance(vitalik_address)
        if balance:
            print(f'✓ Account balance fetched: {balance["balance_eth"]:.2f} ETH')
        else:
            print('✗ No balance data received')
        
        # Test transaction history
        print('Testing transaction history...')
        transactions = await scraper.get_transaction_history(vitalik_address, offset=5)
        if transactions:
            print(f'✓ Transaction history fetched: {len(transactions)} transactions')
            if transactions:
                latest_tx = transactions[0]
                print(f'  Latest tx: {latest_tx["hash"][:10]}... ({latest_tx["value_eth"]:.4f} ETH)')
        else:
            print('✗ No transaction data received')
        
        # Test metrics
        metrics = await scraper.get_metrics()
        print(f'✓ Scraper metrics: {metrics["success_rate"]:.1%} success rate, {metrics["avg_response_time_ms"]:.0f}ms avg response')
        
        await scraper.close()
        return True
        
    except Exception as e:
        print(f'✗ Etherscan scraper test failed: {e}')
        return False

async def test_binance_scraper():
    """Test Binance scraper with real API calls."""
    print('\n=== BINANCE SCRAPER TEST ===')
    
    try:
        from binance_scraper import BinanceScraper
        
        scraper = BinanceScraper()
        
        # Test health check
        print('Testing health check...')
        health = await scraper.health_check()
        print(f'✓ Health check: {"PASS" if health else "FAIL"}')
        
        # Test funding rates
        print('Testing funding rates...')
        funding_rates = await scraper.get_funding_rates(limit=10)
        if funding_rates:
            print(f'✓ Funding rates fetched: {len(funding_rates)} symbols')
            btc_rate = next((r for r in funding_rates if r['symbol'] == 'BTCUSDT'), None)
            if btc_rate:
                print(f'  BTC funding rate: {float(btc_rate["fundingRate"]) * 100:.4f}%')
        else:
            print('✗ No funding rate data received')
        
        # Test open interest
        print('Testing open interest...')
        oi_data = await scraper.get_open_interest(limit=10)
        if oi_data:
            print(f'✓ Open interest fetched: {len(oi_data)} symbols')
            btc_oi = next((r for r in oi_data if r['symbol'] == 'BTCUSDT'), None)
            if btc_oi:
                print(f'  BTC open interest: ${float(btc_oi["sumOpenInterestUsd"]):,.0f}')
        else:
            print('✗ No open interest data received')
        
        await scraper.close()
        return True
        
    except Exception as e:
        print(f'✗ Binance scraper test failed: {e}')
        return False

async def test_dexscreener_scraper():
    """Test DexScreener scraper with real API calls."""
    print('\n=== DEXSCREENER SCRAPER TEST ===')
    
    try:
        from dexscreener_scraper import DexScreenerScraper
        
        scraper = DexScreenerScraper()
        
        # Test health check
        print('Testing health check...')
        health = await scraper.health_check()
        print(f'✓ Health check: {"PASS" if health else "FAIL"}')
        
        # Test trending pairs
        print('Testing trending pairs...')
        trending_pairs = await scraper.get_trending_pairs(limit=10)
        if trending_pairs:
            print(f'✓ Trending pairs fetched: {len(trending_pairs)} pairs')
            if trending_pairs:
                top_pair = trending_pairs[0]
                print(f'  Top pair: {top_pair["baseToken"]["symbol"]}/{top_pair["quoteToken"]["symbol"]} - ${float(top_pair["priceUsd"]):.6f}')
        else:
            print('✗ No trending pairs data received')
        
        # Test new pairs
        print('Testing new pairs...')
        new_pairs = await scraper.get_new_pairs(limit=10)
        if new_pairs:
            print(f'✓ New pairs fetched: {len(new_pairs)} pairs')
            if new_pairs:
                newest_pair = new_pairs[0]
                print(f'  Newest pair: {newest_pair["baseToken"]["symbol"]}/{newest_pair["quoteToken"]["symbol"]}')
        else:
            print('✗ No new pairs data received')
        
        await scraper.close()
        return True
        
    except Exception as e:
        print(f'✗ DexScreener scraper test failed: {e}')
        return False

async def test_fear_greed_scraper():
    """Test Fear & Greed scraper with real API calls."""
    print('\n=== FEAR & GREED SCRAPER TEST ===')
    
    try:
        from fear_greed_scraper import FearGreedScraper
        
        scraper = FearGreedScraper()
        
        # Test health check
        print('Testing health check...')
        health = await scraper.health_check()
        print(f'✓ Health check: {"PASS" if health else "FAIL"}')
        
        # Test current sentiment
        print('Testing current sentiment...')
        sentiment = await scraper.get_current_sentiment()
        if sentiment and 'value' in sentiment:
            value = int(sentiment['value'])
            classification = sentiment['value_classification']
            print(f'✓ Current sentiment: {value}/100 ({classification})')
        else:
            print('✗ No sentiment data received')
        
        # Test historical sentiment
        print('Testing historical sentiment...')
        historical = await scraper.get_historical_sentiment(limit=7)
        if historical:
            print(f'✓ Historical sentiment fetched: {len(historical)} data points')
            avg_value = sum(int(d['value']) for d in historical) / len(historical)
            print(f'  7-day average: {avg_value:.1f}/100')
        else:
            print('✗ No historical sentiment data received')
        
        await scraper.close()
        return True
        
    except Exception as e:
        print(f'✗ Fear & Greed scraper test failed: {e}')
        return False

async def main():
    """Run all scraper validation tests."""
    print('=== DATA SCRAPER VALIDATION WITH LIVE APIs ===')
    print(f'Test started at: {datetime.now().isoformat()}')
    
    start_time = time.time()
    results = {}
    
    # Test each scraper
    scrapers = [
        ('CoinGecko', test_coingecko_scraper),
        ('Etherscan', test_etherscan_scraper),
        ('Binance', test_binance_scraper),
        ('DexScreener', test_dexscreener_scraper),
        ('Fear & Greed', test_fear_greed_scraper),
    ]
    
    for scraper_name, test_func in scrapers:
        try:
            results[scraper_name] = await test_func()
        except Exception as e:
            print(f'✗ {scraper_name} test failed with exception: {e}')
            results[scraper_name] = False
    
    # Summary
    total_time = time.time() - start_time
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f'\n=== SCRAPER VALIDATION SUMMARY ===')
    print(f'Total scrapers tested: {total}')
    print(f'Passed: {passed}')
    print(f'Failed: {total - passed}')
    print(f'Success rate: {passed/total*100:.1f}%')
    print(f'Total test time: {total_time:.2f} seconds')
    
    for scraper_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f'  {scraper_name}: {status}')

if __name__ == "__main__":
    asyncio.run(main())
