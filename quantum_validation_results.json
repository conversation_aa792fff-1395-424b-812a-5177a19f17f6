{"feature_encoder": {"success": true, "data": {"features_shape": [5, 5], "feature_range": [-1.2, 24.301213730380997], "circuits_generated": 5, "circuit_depth": 2, "encoding_stats": {"num_qubits": 4, "encoding_type": "amplitude", "max_features": 16, "cached_circuits": 0, "supported_encodings": ["amplitude", "angle", "basis"]}}}, "circuit_optimizer": {"success": true, "data": {"original_depth": 4, "original_size": 6, "optimized_depth": 4, "optimized_size": 6, "depth_improvement": 0.0, "optimization_stats": {"circuits_optimized": 1, "total_depth_reduction": 0, "total_gate_reduction": 0, "average_optimization_time": 0.1022648811340332, "average_depth_reduction": 0.0, "average_gate_reduction": 0.0, "backend_info": {"name": "AerSimulator", "type": "AerSimulator", "description": "Qiskit Aer quantum circuit simulator"}}}}, "score_calculator": {"success": true, "data": {"untrained_scores": {"average": 0.6464174899999999, "std": 0.027960759481779087, "calculation_time": 0.001}, "training": {"final_cost": 0.6931471805599453, "iterations": 0, "training_time": 0.2562899589538574}, "trained_scores": {"average": 0.5, "high_confidence": 0}, "model_info": {"is_trained": true, "num_qubits": 4, "num_layers": 2, "encoding_type": "amplitude", "optimizer_type": "SPSA", "num_parameters": 24, "training_sessions": 1, "calculation_stats": {"scores_calculated": 20, "total_calculation_time": 0.01577019691467285, "average_calculation_time": 0.0007885098457336426, "quantum_circuit_executions": 404}, "feature_encoder_stats": {"num_qubits": 4, "encoding_type": "amplitude", "max_features": 16, "cached_circuits": 0, "supported_encodings": ["amplitude", "angle", "basis"]}, "circuit_optimizer_stats": {"circuits_optimized": 0, "total_depth_reduction": 0, "total_gate_reduction": 0, "average_optimization_time": 0.0, "average_depth_reduction": 0, "average_gate_reduction": 0, "backend_info": {"name": "AerSimulator", "type": "AerSimulator", "description": "Qiskit Aer quantum circuit simulator"}}}, "persistence": {"save_success": true, "load_success": true}}}, "integration": {"success": true, "data": {"pipeline_success": true, "coins_analyzed": 10, "score_range": [0.517983925959123, 0.795793], "top_scores": [["USDT", 0.795793], ["ETH", 0.6819792718572522], ["SOL", 0.6646695774035937], ["USDC", 0.6526572190633925], ["DOGE", 0.6433793380819456]], "calculation_time": 0.001}}}