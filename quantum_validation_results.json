{"feature_encoder": {"success": true, "data": {"features_shape": [5, 5], "feature_range": [-1.2, 24.301213730380997], "circuits_generated": 5, "circuit_depth": 1, "encoding_stats": {"num_qubits": 4, "encoding_type": "amplitude", "max_features": 16, "cached_circuits": 0, "supported_encodings": ["amplitude", "angle", "basis"]}}}, "circuit_optimizer": {"success": true, "data": {"original_depth": 4, "original_size": 6, "optimized_depth": 4, "optimized_size": 6, "depth_improvement": 0.0, "optimization_stats": {"circuits_optimized": 1, "total_depth_reduction": 0, "total_gate_reduction": 0, "average_optimization_time": 0.08773088455200195, "average_depth_reduction": 0.0, "average_gate_reduction": 0.0, "backend_info": {"name": "AerSimulator", "type": "AerSimulator", "description": "Qiskit Aer quantum circuit simulator"}}}}, "score_calculator": {"success": true, "data": {"untrained_scores": {"average": 0.6547312999999999, "std": 0.029666045322118674, "calculation_time": 0.001}, "training": {"final_cost": 0.41002977881898234, "iterations": 20, "training_time": 26.12149405479431}, "trained_scores": {"average": 0.7313481961641708, "high_confidence": 9}, "model_info": {"is_trained": true, "num_qubits": 4, "num_layers": 2, "encoding_type": "amplitude", "optimizer_type": "SPSA", "num_parameters": 24, "training_sessions": 1, "calculation_stats": {"scores_calculated": 20, "total_calculation_time": 0.0580291748046875, "average_calculation_time": 0.002901458740234375, "quantum_circuit_executions": 9524}, "feature_encoder_stats": {"num_qubits": 4, "encoding_type": "amplitude", "max_features": 16, "cached_circuits": 0, "supported_encodings": ["amplitude", "angle", "basis"]}, "circuit_optimizer_stats": {"circuits_optimized": 0, "total_depth_reduction": 0, "total_gate_reduction": 0, "average_optimization_time": 0.0, "average_depth_reduction": 0, "average_gate_reduction": 0, "backend_info": {"name": "AerSimulator", "type": "AerSimulator", "description": "Qiskit Aer quantum circuit simulator"}}}, "persistence": {"save_success": true, "load_success": true}}}, "integration": {"success": true, "data": {"pipeline_success": true, "coins_analyzed": 10, "score_range": [0.5218782988096098, 0.7957354], "top_scores": [["USDT", 0.7957354], ["USDC", 0.7304721230362696], ["ETH", 0.6709207255926786], ["SOL", 0.6687790791180204], ["DOGE", 0.647192956756425]], "calculation_time": 0.001}}}